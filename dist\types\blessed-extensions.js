/**
 * Blessed.js Type Extensions
 *
 * Custom type definitions and extensions for blessed.js to fix
 * TypeScript compilation issues and add missing properties.
 */
// Utility type for converting TPosition to number
export function toNumber(value) {
    if (typeof value === 'number') {
        return value;
    }
    if (typeof value === 'string') {
        if (value === 'center') {
            return 0; // Will need context-specific handling
        }
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
}
// Type guards
export function isExtendedListElement(element) {
    return element && typeof element.selected !== 'undefined';
}
export function isExtendedTextareaElement(element) {
    return element && typeof element.blur === 'function';
}
export function isExtendedBoxElement(element) {
    return element && typeof element.scrollable !== 'undefined';
}
//# sourceMappingURL=blessed-extensions.js.map