/**
 * Simple state management hook for terminal UI components
 * Provides React-like useState functionality for blessed.js components
 */
export type StateUpdater<T> = (value: T | ((prev: T) => T)) => void;
export interface StateHook<T> {
    value: T;
    setValue: StateUpdater<T>;
}
/**
 * Create a state hook with getter and setter
 */
export declare function useState<T>(initialValue: T): [() => T, StateUpdater<T>];
/**
 * Create a state manager for complex state objects
 */
export declare class StateManager<T extends Record<string, any>> {
    private state;
    private listeners;
    constructor(initialState: T);
    getState(): T;
    setState(updates: Partial<T> | ((prev: T) => Partial<T>)): void;
    subscribe(listener: (state: T) => void): () => void;
    private notifyListeners;
}
/**
 * Create a reactive state hook that can trigger re-renders
 */
export declare function useReactiveState<T>(initialValue: T, onUpdate?: (value: T) => void): [() => T, StateUpdater<T>];
/**
 * Create a computed state that depends on other state values
 */
export declare function useComputed<T, D extends any[]>(dependencies: D, computeFn: (...deps: D) => T): () => T;
/**
 * Create a state hook with persistence to localStorage-like storage
 */
export declare function usePersistedState<T>(key: string, initialValue: T, storage?: {
    getItem: (key: string) => string | null;
    setItem: (key: string, value: string) => void;
}): [() => T, StateUpdater<T>];
/**
 * Create a debounced state hook that delays updates
 */
export declare function useDebouncedState<T>(initialValue: T, delay?: number): [() => T, StateUpdater<T>, () => T];
//# sourceMappingURL=use-state.d.ts.map