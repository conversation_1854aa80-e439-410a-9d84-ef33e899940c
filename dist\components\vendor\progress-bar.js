/**
 * Progress Bar Component
 *
 * Provides customizable progress bars with percentage display,
 * different styles, and smooth animations.
 */
import blessed from 'blessed';
export class ProgressBar {
    element;
    barElement;
    labelElement;
    min;
    max;
    currentValue;
    showPercentage;
    showValue;
    orientation;
    char;
    emptyChar;
    constructor(options) {
        this.min = options.min || 0;
        this.max = options.max || 100;
        this.currentValue = options.value || this.min;
        this.showPercentage = options.showPercentage !== false;
        this.showValue = options.showValue || false;
        this.orientation = options.orientation || 'horizontal';
        this.char = options.char || '█';
        this.emptyChar = options.emptyChar || '░';
        // Create main container
        this.element = blessed.box({
            parent: options.parent,
            top: options.top || 0,
            left: options.left || 0,
            width: options.width || '50%',
            height: options.height || 3,
            border: options.border ? { type: 'line' } : undefined,
            style: {
                fg: 'white',
                bg: 'black',
                ...options.style,
            },
            tags: false,
        });
        // Create label if provided
        if (options.label) {
            this.labelElement = blessed.box({
                parent: this.element,
                top: 0,
                left: 0,
                width: '100%',
                height: 1,
                content: options.label,
                style: {
                    fg: 'white',
                    bg: 'transparent',
                },
                tags: false,
            });
        }
        // Create progress bar
        const barTop = options.label ? 1 : 0;
        const barHeight = options.border ?
            (options.label ? 1 : 2) :
            (options.label ? 1 : '100%');
        this.barElement = blessed.box({
            parent: this.element,
            top: barTop,
            left: options.border ? 1 : 0,
            width: options.border ? '100%-2' : '100%',
            height: barHeight,
            style: {
                fg: 'green',
                bg: 'black',
                ...options.barStyle,
            },
            tags: false,
        });
        this.updateDisplay();
    }
    /**
     * Set progress value
     */
    setValue(value) {
        this.currentValue = Math.max(this.min, Math.min(this.max, value));
        this.updateDisplay();
    }
    /**
     * Get current value
     */
    getValue() {
        return this.currentValue;
    }
    /**
     * Set minimum value
     */
    setMin(min) {
        this.min = min;
        this.currentValue = Math.max(this.min, this.currentValue);
        this.updateDisplay();
    }
    /**
     * Set maximum value
     */
    setMax(max) {
        this.max = max;
        this.currentValue = Math.min(this.max, this.currentValue);
        this.updateDisplay();
    }
    /**
     * Increment progress
     */
    increment(amount = 1) {
        this.setValue(this.currentValue + amount);
    }
    /**
     * Decrement progress
     */
    decrement(amount = 1) {
        this.setValue(this.currentValue - amount);
    }
    /**
     * Get progress percentage
     */
    getPercentage() {
        if (this.max === this.min) {
            return 100;
        }
        return Math.round(((this.currentValue - this.min) / (this.max - this.min)) * 100);
    }
    /**
     * Check if progress is complete
     */
    isComplete() {
        return this.currentValue >= this.max;
    }
    /**
     * Reset progress to minimum
     */
    reset() {
        this.setValue(this.min);
    }
    /**
     * Set progress to maximum
     */
    complete() {
        this.setValue(this.max);
    }
    /**
     * Update the visual display
     */
    updateDisplay() {
        const percentage = this.getPercentage();
        const barWidth = this.barElement.width || 20;
        const barHeight = this.barElement.height || 1;
        let content = '';
        if (this.orientation === 'horizontal') {
            const filledWidth = Math.round((barWidth * percentage) / 100);
            const emptyWidth = barWidth - filledWidth;
            content = this.char.repeat(filledWidth) + this.emptyChar.repeat(emptyWidth);
            // Add percentage or value display
            if (this.showPercentage || this.showValue) {
                const displayText = this.showValue ?
                    `${this.currentValue}/${this.max}` :
                    `${percentage}%`;
                // Center the text on the bar
                const textPos = Math.max(0, Math.floor((barWidth - displayText.length) / 2));
                const contentArray = content.split('');
                for (let i = 0; i < displayText.length && textPos + i < contentArray.length; i++) {
                    contentArray[textPos + i] = displayText[i];
                }
                content = contentArray.join('');
            }
        }
        else {
            // Vertical orientation
            const filledHeight = Math.round((barHeight * percentage) / 100);
            const lines = [];
            for (let i = 0; i < barHeight; i++) {
                if (i < barHeight - filledHeight) {
                    lines.push(this.emptyChar.repeat(barWidth));
                }
                else {
                    lines.push(this.char.repeat(barWidth));
                }
            }
            content = lines.join('\n');
        }
        this.barElement.setContent(content);
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    /**
     * Set label text
     */
    setLabel(label) {
        if (this.labelElement) {
            this.labelElement.setContent(label);
            if (this.element.screen) {
                this.element.screen.render();
            }
        }
    }
    /**
     * Animate progress to target value
     */
    animateTo(targetValue, duration = 1000) {
        return new Promise((resolve) => {
            const startValue = this.currentValue;
            const startTime = Date.now();
            const valueRange = targetValue - startValue;
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                // Easing function (ease-out)
                const easedProgress = 1 - Math.pow(1 - progress, 3);
                const currentValue = startValue + (valueRange * easedProgress);
                this.setValue(currentValue);
                if (progress < 1) {
                    setTimeout(animate, 16); // ~60fps
                }
                else {
                    this.setValue(targetValue);
                    resolve();
                }
            };
            animate();
        });
    }
    /**
     * Get the underlying blessed element
     */
    getElement() {
        return this.element;
    }
    /**
     * Show the progress bar
     */
    show() {
        this.element.show();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    /**
     * Hide the progress bar
     */
    hide() {
        this.element.hide();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    /**
     * Destroy the progress bar
     */
    destroy() {
        this.element.destroy();
    }
}
/**
 * Create a simple progress bar
 */
export function createSimpleProgressBar(parent, options = {}) {
    return new ProgressBar({
        parent,
        width: '80%',
        height: 3,
        border: true,
        showPercentage: true,
        ...options,
    });
}
/**
 * Create a loading progress bar
 */
export function createLoadingProgressBar(parent, label, position = { top: 'center', left: 'center' }) {
    return new ProgressBar({
        parent,
        top: position.top,
        left: position.left,
        width: '60%',
        height: 4,
        border: true,
        label,
        showPercentage: true,
        style: {
            border: { fg: 'cyan' },
        },
        barStyle: {
            fg: 'green',
        },
    });
}
export default ProgressBar;
//# sourceMappingURL=progress-bar.js.map