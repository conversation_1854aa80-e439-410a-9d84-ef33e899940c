/**
 * Package Manager Detection
 *
 * Detects which package manager was used to install the CLI
 * and provides appropriate update commands.
 */
export type AgentName = 'npm' | 'pnpm' | 'yarn' | 'bun' | 'deno';
/**
 * Detect package manager by installation path
 */
export declare function detectInstallerByPath(): Promise<AgentName | undefined>;
/**
 * Get install command for package manager
 */
export declare function getInstallCommand(agent: AgentName, packageName: string, global?: boolean): string;
/**
 * Get update command for package manager
 */
export declare function getUpdateCommand(agent: AgentName, packageName: string, global?: boolean): string;
/**
 * Get uninstall command for package manager
 */
export declare function getUninstallCommand(agent: AgentName, packageName: string, global?: boolean): string;
/**
 * Get package manager version
 */
export declare function getAgentVersion(agent: AgentName): Promise<string | null>;
/**
 * Get all available package managers
 */
export declare function getAvailableAgents(): Promise<Array<{
    name: AgentName;
    version: string | null;
    available: boolean;
}>>;
/**
 * Detect package manager from package.json scripts
 */
export declare function detectFromPackageJson(packageJsonPath: string): AgentName | undefined;
/**
 * Get recommended package manager
 */
export declare function getRecommendedAgent(): Promise<AgentName>;
//# sourceMappingURL=package-manager-detector.d.ts.map