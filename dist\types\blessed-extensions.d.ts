/**
 * Blessed.js Type Extensions
 *
 * Custom type definitions and extensions for blessed.js to fix
 * TypeScript compilation issues and add missing properties.
 */
import * as blessed from 'blessed';
export type BlessedStyle = blessed.Widgets.Types.TStyle;
export type BlessedBorder = blessed.Widgets.Border;
export type BlessedKeyEvent = blessed.Widgets.Events.IKeyEventArg;
export type BlessedElement = blessed.Widgets.BlessedElement;
export type BlessedScreen = blessed.Widgets.Screen;
export type BlessedScreenOptions = blessed.Widgets.IScreenOptions;
export interface ExtendedListElement extends blessed.Widgets.ListElement {
    selected: number;
    focused: boolean;
}
export interface ExtendedTextareaElement extends blessed.Widgets.TextareaElement {
    focused: boolean;
    blur(): void;
}
export interface ExtendedBoxElement extends blessed.Widgets.BoxElement {
    padding?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
    scrollable?: boolean;
    alwaysScroll?: boolean;
    blur?(): void;
}
export interface ExtendedScreen extends blessed.Widgets.Screen {
    saveCursor?(): void;
    restoreCursor?(): void;
    screenshot(): string;
}
export type BlessedForm = blessed.Widgets.FormElement<any>;
export interface NumericPosition {
    top: number;
    left: number;
    width: number;
    height: number;
}
export declare function toNumber(value: blessed.Widgets.Types.TPosition): number;
export declare function isExtendedListElement(element: any): element is ExtendedListElement;
export declare function isExtendedTextareaElement(element: any): element is ExtendedTextareaElement;
export declare function isExtendedBoxElement(element: any): element is ExtendedBoxElement;
export interface ExtendedCursor extends blessed.Widgets.Types.TCursor {
    color: string;
}
export interface ExtendedListOptions extends Omit<blessed.Widgets.ListOptions<blessed.Widgets.ListElementStyle>, 'items'> {
    items?: (string | {
        text: string;
        value?: any;
    })[];
}
//# sourceMappingURL=blessed-extensions.d.ts.map