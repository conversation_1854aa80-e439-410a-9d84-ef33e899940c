/**
 * Enhanced Box Component
 *
 * Wrapper around blessed.box with additional functionality
 * for layout management and styling.
 */
import * as blessed from 'blessed';
import { EventEmitter } from 'events';
import { toNumber } from '../../../types/blessed-extensions.js';
export class EnhancedBox extends EventEmitter {
    box;
    options;
    children = [];
    constructor(options = {}) {
        super();
        this.options = options;
        // Apply theme
        const themedOptions = this.applyTheme(options);
        // Create blessed box with enhanced defaults
        this.box = blessed.box({
            scrollable: false,
            alwaysScroll: false,
            mouse: true,
            keys: false,
            ...themedOptions,
        });
        this.setupEventHandlers();
        this.applySpacing();
    }
    /**
     * Apply theme styling
     */
    applyTheme(options) {
        const themes = {
            default: {
                style: {
                    fg: 'white',
                    bg: 'black',
                    border: { fg: 'white' },
                },
            },
            primary: {
                style: {
                    fg: 'white',
                    bg: 'blue',
                    border: { fg: 'cyan' },
                },
            },
            secondary: {
                style: {
                    fg: 'black',
                    bg: 'gray',
                    border: { fg: 'white' },
                },
            },
            success: {
                style: {
                    fg: 'white',
                    bg: 'green',
                    border: { fg: 'green' },
                },
            },
            warning: {
                style: {
                    fg: 'black',
                    bg: 'yellow',
                    border: { fg: 'yellow' },
                },
            },
            error: {
                style: {
                    fg: 'white',
                    bg: 'red',
                    border: { fg: 'red' },
                },
            },
        };
        const theme = themes[options.theme || 'default'];
        return {
            ...options,
            style: {
                ...theme.style,
                ...options.style,
            },
        };
    }
    /**
     * Apply padding and margin
     */
    applySpacing() {
        // Apply padding (using type assertion since blessed types don't include padding)
        if (this.options.padding !== undefined) {
            if (typeof this.options.padding === 'number') {
                this.box.padding = {
                    top: this.options.padding,
                    right: this.options.padding,
                    bottom: this.options.padding,
                    left: this.options.padding,
                };
            }
            else {
                this.box.padding = {
                    top: this.options.padding.top || 0,
                    right: this.options.padding.right || 0,
                    bottom: this.options.padding.bottom || 0,
                    left: this.options.padding.left || 0,
                };
            }
        }
        // Apply margin (handled through positioning)
        if (this.options.margin !== undefined) {
            const margin = typeof this.options.margin === 'number'
                ? { top: this.options.margin, right: this.options.margin, bottom: this.options.margin, left: this.options.margin }
                : this.options.margin;
            // Adjust position based on margin
            if (margin.top) {
                this.box.top = (this.box.top || 0) + margin.top;
            }
            if (margin.left) {
                this.box.left = (this.box.left || 0) + margin.left;
            }
            if (margin.right) {
                this.box.width = `100%-${margin.right}`;
            }
            if (margin.bottom) {
                this.box.height = `100%-${margin.bottom}`;
            }
        }
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Handle resize events
        this.box.on('resize', () => {
            const width = this.box.width;
            const height = this.box.height;
            if (this.options.onResize) {
                this.options.onResize(width, height);
            }
            this.emit('resize', width, height);
        });
        // Handle focus events
        this.box.on('focus', () => {
            if (this.options.onFocus) {
                this.options.onFocus();
            }
            this.emit('focus');
        });
        // Handle blur events
        this.box.on('blur', () => {
            if (this.options.onBlur) {
                this.options.onBlur();
            }
            this.emit('blur');
        });
        // Handle click events
        this.box.on('click', (data) => {
            if (this.options.onClick) {
                this.options.onClick(data);
            }
            this.emit('click', data);
        });
    }
    /**
     * Get the underlying blessed box
     */
    getBox() {
        return this.box;
    }
    /**
     * Set content
     */
    setContent(content) {
        this.box.setContent(content);
        this.render();
    }
    /**
     * Get content
     */
    getContent() {
        return this.box.getContent();
    }
    /**
     * Append content
     */
    appendContent(content) {
        const currentContent = this.box.getContent();
        this.box.setContent(currentContent + content);
        this.render();
    }
    /**
     * Clear content
     */
    clear() {
        this.box.setContent('');
        this.render();
    }
    /**
     * Add child element
     */
    append(child) {
        this.box.append(child);
        this.children.push(child);
        this.render();
    }
    /**
     * Remove child element
     */
    remove(child) {
        this.box.remove(child);
        const index = this.children.indexOf(child);
        if (index !== -1) {
            this.children.splice(index, 1);
        }
        this.render();
    }
    /**
     * Insert child at index
     */
    insert(child, index) {
        this.box.insert(child, index);
        this.children.splice(index, 0, child);
        this.render();
    }
    /**
     * Get all children
     */
    getChildren() {
        return [...this.children];
    }
    /**
     * Focus the box
     */
    focus() {
        this.box.focus();
    }
    /**
     * Show the box
     */
    show() {
        this.box.show();
        this.render();
    }
    /**
     * Hide the box
     */
    hide() {
        this.box.hide();
        this.render();
    }
    /**
     * Toggle visibility
     */
    toggle() {
        if (this.box.visible) {
            this.hide();
        }
        else {
            this.show();
        }
    }
    /**
     * Set position
     */
    setPosition(options) {
        if (options.top !== undefined) {
            this.box.top = options.top;
        }
        if (options.left !== undefined) {
            this.box.left = options.left;
        }
        if (options.width !== undefined) {
            this.box.width = options.width;
        }
        if (options.height !== undefined) {
            this.box.height = options.height;
        }
        this.render();
    }
    /**
     * Get position
     */
    getPosition() {
        return {
            top: toNumber(this.box.atop || 0),
            left: toNumber(this.box.aleft || 0),
            width: toNumber(this.box.width || 0),
            height: toNumber(this.box.height || 0),
        };
    }
    /**
     * Set style
     */
    setStyle(style) {
        Object.assign(this.box.style, style);
        this.render();
    }
    /**
     * Set border
     */
    setBorder(border) {
        this.box.border = border;
        this.render();
    }
    /**
     * Enable scrolling
     */
    enableScrolling() {
        this.box.scrollable = true;
        this.box.alwaysScroll = true;
        this.render();
    }
    /**
     * Disable scrolling
     */
    disableScrolling() {
        this.box.scrollable = false;
        this.box.alwaysScroll = false;
        this.render();
    }
    /**
     * Scroll to position
     */
    scrollTo(position) {
        this.box.scrollTo(position);
        this.render();
    }
    /**
     * Scroll by amount
     */
    scroll(amount) {
        this.box.scroll(amount);
        this.render();
    }
    /**
     * Get scroll position
     */
    getScrollPosition() {
        return this.box.getScrollHeight();
    }
    /**
     * Render the box
     */
    render() {
        if (this.box.screen) {
            this.box.screen.render();
        }
    }
    /**
     * Check if box is visible
     */
    get visible() {
        return this.box.visible;
    }
    /**
     * Get box dimensions
     */
    get dimensions() {
        return {
            width: this.box.width || 0,
            height: this.box.height || 0,
        };
    }
    /**
     * Destroy the box
     */
    destroy() {
        // Remove all children
        for (const child of this.children) {
            this.box.remove(child);
        }
        this.children = [];
        // Destroy the box
        this.box.destroy();
        this.removeAllListeners();
    }
}
export default EnhancedBox;
//# sourceMappingURL=box.js.map