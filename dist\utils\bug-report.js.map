{"version": 3, "file": "bug-report.js", "sourceRoot": "", "sources": ["../../src/utils/bug-report.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAc5C;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAmB;IACnD,MAAM,EACJ,KAAK,EACL,UAAU,EACV,KAAK,EACL,QAAQ,EACR,QAAQ,EAAE,YAAY,EACtB,WAAW,EACX,KAAK,EACL,iBAAiB,GAClB,GAAG,IAAI,CAAC;IAET,uBAAuB;IACvB,MAAM,KAAK,GAAG,KAAK;QACjB,CAAC,CAAC,QAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;QACzC,CAAC,CAAC,iCAAiC,CAAC;IAEtC,sBAAsB;IACtB,MAAM,IAAI,GAAG,iBAAiB,CAAC;QAC7B,KAAK;QACL,UAAU;QACV,KAAK;QACL,QAAQ;QACR,QAAQ,EAAE,YAAY;QACtB,WAAW;QACX,KAAK;QACL,iBAAiB;KAClB,CAAC,CAAC;IAEH,iBAAiB;IACjB,MAAM,YAAY,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC/C,MAAM,WAAW,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAE7C,wDAAwD;IACxD,MAAM,OAAO,GAAG,6CAA6C,CAAC;IAE9D,OAAO,GAAG,OAAO,qBAAqB,YAAY,SAAS,WAAW,aAAa,CAAC;AACtF,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,IAAmB;IAC5C,MAAM,EACJ,KAAK,EACL,UAAU,EACV,KAAK,EACL,QAAQ,EACR,QAAQ,EAAE,YAAY,EACtB,WAAW,EACX,KAAK,EACL,iBAAiB,GAClB,GAAG,IAAI,CAAC;IAET,MAAM,QAAQ,GAAa,EAAE,CAAC;IAE9B,kBAAkB;IAClB,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACpC,IAAI,KAAK,EAAE,CAAC;QACV,QAAQ,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7C,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClB,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAClC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3B,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;SAAM,CAAC;QACN,QAAQ,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAClE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpB,CAAC;IAED,qBAAqB;IACrB,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACvC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,UAAU,EAAE,CAAC,CAAC;IAClD,QAAQ,CAAC,IAAI,CAAC,0BAA0B,WAAW,EAAE,CAAC,CAAC;IACvD,QAAQ,CAAC,IAAI,CAAC,mBAAmB,YAAY,EAAE,CAAC,CAAC;IACjD,QAAQ,CAAC,IAAI,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;IAChD,QAAQ,CAAC,IAAI,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;IAC1C,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAElB,qBAAqB;IACrB,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACvC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,MAAM,cAAc,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;QACrD,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,QAAQ,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QACzE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IACD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAElB,8BAA8B;IAC9B,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACtC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;IACzD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAElB,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACpC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAClD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAElB,qBAAqB;IACrB,IAAI,iBAAiB,EAAE,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACvC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACjC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpB,CAAC;IAED,8BAA8B;IAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACjC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3B,QAAQ,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACpE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACnE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpB,CAAC;IAED,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAAC,KAA+C;IAC7E,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,gBAAgB;QACrD,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,OAAO,EAAE,IAAI,KAAK,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBACxC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,kBAAkB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC5F,UAAU,EAAE,CAAC;YACf,CAAC;iBAAM,IAAI,OAAO,EAAE,IAAI,KAAK,aAAa,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBAC3D,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBACxC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,mBAAmB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC7F,UAAU,EAAE,CAAC;YACf,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACzC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,0BAA0B,CAAC,CAAC;YACpD,UAAU,EAAE,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,kBAAkB,CAAC,CAAC;YAC5C,UAAU,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,KAA+C;IAC1E,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACtB,MAAM,SAAS,GAAQ;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;QAEF,mBAAmB;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC7C,MAAM,gBAAgB,GAAQ;oBAC5B,IAAI,EAAE,OAAO,CAAC,IAAI;iBACnB,CAAC;gBAEF,oCAAoC;gBACpC,IAAI,OAAO,CAAC,IAAI,KAAK,YAAY,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;oBACpE,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;oBAChC,2CAA2C;oBAC3C,gBAAgB,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC7C,CAAC;gBAED,OAAO,gBAAgB,CAAC;YAC1B,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,IAAY;IAChC,8BAA8B;IAC9B,MAAM,iBAAiB,GAAG;QACxB,uBAAuB,EAAY,uBAAuB;QAC1D,0BAA0B,EAAS,kBAAkB;QACrD,0BAA0B,EAAS,gBAAgB;QACnD,+BAA+B,EAAI,gBAAgB;QACnD,yBAAyB,EAAU,YAAY;QAC/C,sBAAsB,EAAa,SAAS;QAC5C,uBAAuB,EAAY,UAAU;QAC7C,6CAA6C,EAAE,sBAAsB;KACtE,CAAC;IAEF,IAAI,SAAS,GAAG,IAAI,CAAC;IAErB,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;QACxC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa;IAK3B,OAAO;QACL,QAAQ,EAAE,GAAG,QAAQ,EAAE,IAAI,IAAI,EAAE,IAAI,OAAO,EAAE,EAAE;QAChD,WAAW,EAAE,OAAO,EAAE;QACtB,UAAU,EAAE,WAAW;KACxB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CACjC,KAA+C,EAC/C,KAAa,EACb,QAAgB,EAChB,KAAa,EACb,iBAA0B;IAE1B,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;IAEnC,OAAO;QACL,KAAK;QACL,KAAK;QACL,QAAQ;QACR,KAAK;QACL,iBAAiB;QACjB,GAAG,UAAU;KACd,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,sBAAsB,CACpC,KAAY,EACZ,OAAgB;IAEhB,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;IAEnC,MAAM,IAAI,GAAkB;QAC1B,KAAK,EAAE,EAAE;QACT,KAAK,EAAE,SAAS;QAChB,QAAQ,EAAE,SAAS;QACnB,KAAK;QACL,iBAAiB,EAAE,OAAO;QAC1B,GAAG,UAAU;KACd,CAAC;IAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC"}