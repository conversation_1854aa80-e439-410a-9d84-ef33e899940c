{"version": 3, "file": "providers.js", "sourceRoot": "", "sources": ["../../src/utils/providers.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAIH;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAmC;IACvD,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,2BAA2B;QACpC,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE;YACN,OAAO;YACP,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,oBAAoB;YACpB,sBAAsB;YACtB,eAAe;YACf,mBAAmB;YACnB,YAAY;YACZ,SAAS;YACT,SAAS;SACV;QACD,YAAY,EAAE,OAAO;QACrB,cAAc,EAAE,IAAI;QACpB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,MAAM;KACzB;IAED,KAAK,EAAE;QACL,IAAI,EAAE,cAAc;QACpB,OAAO,EAAE,2EAA2E;QACpF,MAAM,EAAE,sBAAsB;QAC9B,MAAM,EAAE;YACN,OAAO;YACP,aAAa;YACb,cAAc;YACd,kBAAkB;SACnB;QACD,YAAY,EAAE,OAAO;QACrB,cAAc,EAAE,IAAI;QACpB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,MAAM;KACzB;IAED,MAAM,EAAE;QACN,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,kDAAkD;QAC3D,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE;YACN,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,kBAAkB;SACnB;QACD,YAAY,EAAE,YAAY;QAC1B,cAAc,EAAE,IAAI;QACpB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,OAAO;KAC1B;IAED,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,2BAA2B;QACpC,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE;YACN,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,eAAe;YACf,eAAe;YACf,SAAS;YACT,SAAS;YACT,aAAa;YACb,aAAa;SACd;QACD,YAAY,EAAE,QAAQ;QACtB,cAAc,EAAE,KAAK;QACrB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,IAAI;KACvB;IAED,OAAO,EAAE;QACP,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,2BAA2B;QACpC,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE;YACN,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,sBAAsB;YACtB,uBAAuB;YACvB,wBAAwB;SACzB;QACD,YAAY,EAAE,sBAAsB;QACpC,cAAc,EAAE,KAAK;QACrB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,KAAK;KACxB;IAED,QAAQ,EAAE;QACR,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,6BAA6B;QACtC,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE;YACN,eAAe;YACf,gBAAgB;YAChB,eAAe;SAChB;QACD,YAAY,EAAE,gBAAgB;QAC9B,cAAc,EAAE,KAAK;QACrB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,KAAK;KACxB;IAED,GAAG,EAAE;QACH,IAAI,EAAE,KAAK;QACX,OAAO,EAAE,qBAAqB;QAC9B,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE;YACN,WAAW;YACX,kBAAkB;SACnB;QACD,YAAY,EAAE,WAAW;QACzB,cAAc,EAAE,IAAI;QACpB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,MAAM;KACzB;IAED,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,gCAAgC;QACzC,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE;YACN,iBAAiB;YACjB,oBAAoB;YACpB,aAAa;YACb,gBAAgB;YAChB,iBAAiB;SAClB;QACD,YAAY,EAAE,iBAAiB;QAC/B,cAAc,EAAE,KAAK;QACrB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,KAAK;KACxB;IAED,OAAO,EAAE;QACP,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,yBAAyB;QAClC,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE;YACN,aAAa;YACb,YAAY;SACb;QACD,YAAY,EAAE,aAAa;QAC3B,cAAc,EAAE,KAAK;QACrB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,IAAI;KACvB;IAED,UAAU,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,8BAA8B;QACvC,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE;YACN,cAAc;YACd,oBAAoB;YACpB,yBAAyB;YACzB,2BAA2B;YAC3B,0BAA0B;YAC1B,mBAAmB;YACnB,6BAA6B;YAC7B,iCAAiC;SAClC;QACD,YAAY,EAAE,cAAc;QAC5B,cAAc,EAAE,IAAI;QACpB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,MAAM;KACzB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,OAAO,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB;IAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,OAAO,IAAI,CAAC,WAAW,EAAE,IAAI,SAAS,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,YAAoB;IAClD,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;IAC3C,OAAO,QAAQ,EAAE,YAAY,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC;AACpE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,YAAoB;IACpD,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;IAC3C,OAAO,QAAQ,EAAE,MAAM,IAAI,EAAE,CAAC;AAChC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,YAAoB;IACjD,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;IAC3C,OAAO,QAAQ,EAAE,cAAc,IAAI,KAAK,CAAC;AAC3C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,YAAoB;IAChD,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;IAC3C,OAAO,QAAQ,EAAE,aAAa,IAAI,KAAK,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,YAAoB;IACtD,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;IAC3C,OAAO,QAAQ,EAAE,gBAAgB,IAAI,IAAI,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,MAAsB;IACrD,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACjB,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACpB,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACnB,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjD,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;QAC3D,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAY,EAAE,MAAsB;IACpE,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACxC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CAAC,mCAAmC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,MAAM,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,IAAY;IAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IACrC,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;QAC3B,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,wBAAwB,CAAC,IAAY;IACnD,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,uCAAuC;IACvC,MAAM,SAAS,GAA4B,EAAE,CAAC;IAE9C,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACjE,IAAI,UAAU,EAAE,CAAC;QACf,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC;IACjC,CAAC;IAED,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAC/D,IAAI,SAAS,EAAE,CAAC;QACd,SAAS,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC;IACrD,CAAC;IAED,OAAO,EAAE,GAAG,QAAQ,EAAE,GAAG,SAAS,EAAE,CAAC;AACvC,CAAC"}