/**
 * Comprehensive Model Information Database
 *
 * Contains detailed metadata for AI models including context lengths,
 * capabilities, and provider-specific information.
 */
export interface ModelInfo {
    id: string;
    name: string;
    provider: string;
    contextLength: number;
    maxOutputTokens?: number;
    supportsImages: boolean;
    supportsTools: boolean;
    supportsStreaming: boolean;
    inputCostPer1kTokens?: number;
    outputCostPer1kTokens?: number;
    description?: string;
    releaseDate?: string;
    deprecated?: boolean;
    aliases?: string[];
}
/**
 * Comprehensive model database with latest models
 */
export declare const MODEL_DATABASE: Record<string, ModelInfo>;
/**
 * Get model information by ID
 */
export declare function getModelInfo(modelId: string): ModelInfo | undefined;
/**
 * Get all models for a provider
 */
export declare function getModelsForProvider(provider: string): ModelInfo[];
/**
 * Search models by name or description
 */
export declare function searchModels(query: string): ModelInfo[];
/**
 * Get models with specific capabilities
 */
export declare function getModelsWithCapabilities(capabilities: {
    supportsImages?: boolean;
    supportsTools?: boolean;
    supportsStreaming?: boolean;
    minContextLength?: number;
}): ModelInfo[];
/**
 * Get recommended models for coding tasks
 */
export declare function getCodingModels(): ModelInfo[];
/**
 * Get models sorted by context length
 */
export declare function getModelsByContextLength(descending?: boolean): ModelInfo[];
/**
 * Check if model is deprecated
 */
export declare function isModelDeprecated(modelId: string): boolean;
/**
 * Get model aliases
 */
export declare function getModelAliases(modelId: string): string[];
/**
 * Resolve model alias to actual model ID
 */
export declare function resolveModelAlias(alias: string): string;
//# sourceMappingURL=model-info.d.ts.map