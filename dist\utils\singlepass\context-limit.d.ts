/**
 * Context Limit Management
 *
 * Handles intelligent file selection and context optimization
 * to fit within model token limits while preserving important information.
 */
import type { FileContent } from '../../types/index.js';
export interface SizeMap {
    totalFiles: number;
    totalSize: number;
    filesByType: Record<string, number>;
    sizeByType: Record<string, number>;
    largestFiles: Array<{
        path: string;
        size: number;
    }>;
}
export interface ContextLimits {
    maxTokens: number;
    maxFiles: number;
    maxFileSize: number;
    priorityExtensions: string[];
    excludeExtensions: string[];
}
/**
 * Compute size map for directory analysis
 */
export declare function computeSizeMap(files: FileContent[]): SizeMap;
/**
 * Optimize file selection for context limits
 */
export declare function optimizeForContextLimits(files: FileContent[], limits: ContextLimits): FileContent[];
/**
 * Get default context limits for a model
 */
export declare function getDefaultContextLimits(model: string): ContextLimits;
/**
 * Analyze context usage
 */
export declare function analyzeContextUsage(files: FileContent[]): {
    totalTokens: number;
    tokensByType: Record<string, number>;
    efficiency: number;
    recommendations: string[];
};
/**
 * Smart file chunking for very large files
 */
export declare function chunkLargeFile(file: FileContent, maxChunkTokens: number): FileContent[];
//# sourceMappingURL=context-limit.d.ts.map