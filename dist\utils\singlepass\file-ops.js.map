{"version": 3, "file": "file-ops.js", "sourceRoot": "", "sources": ["../../../src/utils/singlepass/file-ops.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,aAAa,EAAgB,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC;AAChG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAQ,MAAM,MAAM,CAAC;AAC9C,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC/D,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAY7D;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,QAAgB,EAChB,QAAgB;IAEhB,MAAM,MAAM,GAAwB;QAClC,OAAO,EAAE,KAAK;QACd,aAAa,EAAE,EAAE;QACjB,YAAY,EAAE,EAAE;QAChB,YAAY,EAAE,EAAE;QAChB,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;KACb,CAAC;IAEF,IAAI,CAAC;QACH,OAAO,CAAC,6CAA6C,CAAC,CAAC;QAEvD,wCAAwC;QACxC,MAAM,UAAU,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAEnD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAC7D,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YACtB,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,QAAQ,CAAC,SAAS,UAAU,CAAC,MAAM,kBAAkB,CAAC,CAAC;QAEvD,sBAAsB;QACtB,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;YACxC,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,mBAAmB;QACnB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,kBAAkB,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,SAAS,CAAC,IAAI,OAAO,SAAS,CAAC,IAAI,KAAK,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACpH,CAAC;QACH,CAAC;QAED,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;QAE5C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,OAAO,CAAC,2CAA2C,UAAU,CAAC,MAAM,aAAa,CAAC,CAAC;QACrF,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,2BAA2B,MAAM,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,MAAM,CAAC;IAEhB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,QAAQ,CAAC,mCAAmC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnE,OAAO,MAAM,CAAC;IAChB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAAC,QAAgB;IAC7C,MAAM,UAAU,GAAqB,EAAE,CAAC;IAExC,kCAAkC;IAClC,MAAM,aAAa,GAAG,qEAAqE,CAAC;IAC5F,IAAI,KAAK,CAAC;IAEV,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QACvD,UAAU,CAAC,IAAI,CAAC;YACd,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YACrB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;SAClB,CAAC,CAAC;IACL,CAAC;IAED,gCAAgC;IAChC,MAAM,aAAa,GAAG,wEAAwE,CAAC;IAE/F,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QACvD,UAAU,CAAC,IAAI,CAAC;YACd,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YACrB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;SAClB,CAAC,CAAC;IACL,CAAC;IAED,2BAA2B;IAC3B,MAAM,aAAa,GAAG,0CAA0C,CAAC;IAEjE,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QACvD,UAAU,CAAC,IAAI,CAAC;YACd,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,8BAA8B;IAC9B,MAAM,WAAW,GAAG,+DAA+D,CAAC;IAEpF,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QACrD,UAAU,CAAC,IAAI,CAAC;YACd,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YACrB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;SACzB,CAAC,CAAC;IACL,CAAC;IAED,+BAA+B;IAC/B,MAAM,iBAAiB,GAAG,2HAA2H,CAAC;IAEtJ,OAAO,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAC3D,MAAM,SAAS,GAAmB;YAChC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAS;YACnC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACb,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACtC,CAAC;QAED,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACb,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,UAA4B,EAAE,QAAgB;IACxE,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;QAEnD,yCAAyC;QACzC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,gCAAgC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9D,SAAS;QACX,CAAC;QAED,mCAAmC;QACnC,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,QAAQ;gBACX,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACzB,MAAM,CAAC,IAAI,CAAC,wBAAwB,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBACxD,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,KAAK,EAAE,EAAE,CAAC;oBACnD,MAAM,CAAC,IAAI,CAAC,qCAAqC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBACrE,CAAC;gBACD,MAAM;YAER,KAAK,QAAQ;gBACX,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1B,MAAM,CAAC,IAAI,CAAC,mCAAmC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBACnE,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,KAAK,EAAE,EAAE,CAAC;oBACnD,MAAM,CAAC,IAAI,CAAC,qCAAqC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBACrE,CAAC;gBACD,MAAM;YAER,KAAK,QAAQ;gBACX,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1B,MAAM,CAAC,IAAI,CAAC,qCAAqC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBACrE,CAAC;gBACD,MAAM;YAER,KAAK,MAAM;gBACT,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1B,MAAM,CAAC,IAAI,CAAC,wCAAwC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBACxE,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBACvB,MAAM,CAAC,IAAI,CAAC,oCAAoC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBACpE,CAAC;qBAAM,CAAC;oBACN,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;oBACzD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;wBAC/C,MAAM,CAAC,IAAI,CAAC,oCAAoC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;oBACvE,CAAC;oBACD,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC5B,MAAM,CAAC,IAAI,CAAC,wCAAwC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC3E,CAAC;gBACH,CAAC;gBACD,MAAM;YAER;gBACE,MAAM,CAAC,IAAI,CAAC,2BAA2B,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAC/B,SAAyB,EACzB,QAAgB,EAChB,MAA2B;IAE3B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;IAEnD,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;QACzB,KAAK,QAAQ;YACX,MAAM,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,OAAQ,EAAE,MAAM,CAAC,CAAC;YACvD,MAAM;QAER,KAAK,QAAQ;YACX,MAAM,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,OAAQ,EAAE,MAAM,CAAC,CAAC;YACvD,MAAM;QAER,KAAK,QAAQ;YACX,MAAM,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACnC,MAAM;QAER,KAAK,MAAM;YACT,MAAM,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,OAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;YACxE,MAAM;QAER;YACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IACnE,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,UAAU,CACvB,QAAgB,EAChB,OAAe,EACf,MAA2B;IAE3B,0BAA0B;IAC1B,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACrB,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACtC,CAAC;IAED,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACzC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEnC,QAAQ,CAAC,iBAAiB,QAAQ,EAAE,CAAC,CAAC;AACxC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,UAAU,CACvB,QAAgB,EAChB,OAAe,EACf,MAA2B;IAE3B,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACzC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEpC,QAAQ,CAAC,iBAAiB,QAAQ,EAAE,CAAC,CAAC;AACxC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,UAAU,CACvB,QAAgB,EAChB,MAA2B;IAE3B,UAAU,CAAC,QAAQ,CAAC,CAAC;IACrB,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEnC,QAAQ,CAAC,iBAAiB,QAAQ,EAAE,CAAC,CAAC;AACxC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,QAAQ,CACrB,OAAe,EACf,OAAe,EACf,MAA2B;IAE3B,iCAAiC;IACjC,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IACnC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAC3B,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAClC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAElC,QAAQ,CAAC,eAAe,OAAO,OAAO,OAAO,EAAE,CAAC,CAAC;AACnD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAY;IAK5C,MAAM,MAAM,GAAqE,EAAE,CAAC;IAEpF,8DAA8D;IAC9D,MAAM,gBAAgB,GAAG,iDAAiD,CAAC;IAC3E,IAAI,KAAK,CAAC;IAEV,OAAO,CAAC,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC;YACV,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;YAClB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;YAClB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;SAClB,CAAC,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,4BAA4B,CAAC,IAAY;IACvD,MAAM,UAAU,GAAqB,EAAE,CAAC;IACxC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAE5C,uBAAuB;QACvB,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3H,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;YAC5E,IAAI,SAAS,EAAE,CAAC;gBACd,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;oBAClB,OAAO,EAAE,EAAE;iBACZ,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;YAC/E,IAAI,SAAS,EAAE,CAAC;gBACd,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;iBACnB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;YAC7F,IAAI,SAAS,EAAE,CAAC;gBACd,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;oBAClB,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,UAA4B;IAC9D,MAAM,MAAM,GAAqB,EAAE,CAAC;IACpC,MAAM,OAAO,GAAG,IAAI,GAAG,EAA0B,CAAC;IAElD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,oCAAoC;YACpC,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9D,gDAAgD;gBAChD,QAAQ,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YACvC,CAAC;iBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrE,+CAA+C;gBAC/C,QAAQ,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YACvC,CAAC;iBAAM,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACvC,kCAAkC;gBAClC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACzB,OAAO,QAAQ,CAAC,OAAO,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACtC,CAAC"}