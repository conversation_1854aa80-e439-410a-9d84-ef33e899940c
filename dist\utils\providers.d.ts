/**
 * AI Provider Configuration System
 *
 * Defines supported AI providers with their configurations,
 * including built-in providers and custom provider support.
 */
import type { ProviderConfig } from '../types/index.js';
/**
 * Built-in AI providers with their default configurations
 */
export declare const providers: Record<string, ProviderConfig>;
/**
 * Get provider configuration by name
 */
export declare function getProvider(name: string): ProviderConfig | undefined;
/**
 * Get all available provider names
 */
export declare function getProviderNames(): string[];
/**
 * Check if a provider exists
 */
export declare function hasProvider(name: string): boolean;
/**
 * Get default model for a provider
 */
export declare function getDefaultModel(providerName: string): string;
/**
 * Get available models for a provider
 */
export declare function getProviderModels(providerName: string): string[];
/**
 * Check if provider supports images
 */
export declare function supportsImages(providerName: string): boolean;
/**
 * Check if provider supports tools/function calling
 */
export declare function supportsTools(providerName: string): boolean;
/**
 * Get maximum context length for a provider
 */
export declare function getMaxContextLength(providerName: string): number;
/**
 * Validate provider configuration
 */
export declare function validateProvider(config: ProviderConfig): string[];
/**
 * Add or update a custom provider
 */
export declare function addCustomProvider(name: string, config: ProviderConfig): void;
/**
 * Remove a custom provider
 */
export declare function removeCustomProvider(name: string): boolean;
/**
 * Get provider configuration with environment overrides
 */
export declare function getProviderWithOverrides(name: string): ProviderConfig | undefined;
//# sourceMappingURL=providers.d.ts.map