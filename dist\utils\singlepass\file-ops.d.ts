/**
 * File Operations Processor
 *
 * Processes AI responses containing file operations and applies them
 * to the filesystem with proper validation and error handling.
 */
import type { PatchOperation } from '../../types/index.js';
export interface FileOperationResult {
    success: boolean;
    filesModified: string[];
    filesCreated: string[];
    filesDeleted: string[];
    errors: string[];
    warnings: string[];
}
/**
 * Process file operations from AI response
 */
export declare function processFileOperations(response: string, rootPath: string): Promise<FileOperationResult>;
/**
 * Extract file content from code blocks
 */
export declare function extractCodeBlocks(text: string): Array<{
    language?: string;
    content: string;
    filename?: string;
}>;
/**
 * Detect file operations from natural language
 */
export declare function detectFileOperationsFromText(text: string): PatchOperation[];
/**
 * Merge file operations to avoid conflicts
 */
export declare function mergeFileOperations(operations: PatchOperation[]): PatchOperation[];
//# sourceMappingURL=file-ops.d.ts.map