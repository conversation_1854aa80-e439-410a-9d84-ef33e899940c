/**
 * Session Rollout System
 *
 * Handles automatic session persistence with best-effort async saving
 * without blocking the UI. Provides session recovery and management.
 */
import type { ResponseItem, SessionData } from '../../types/index.js';
/**
 * Save session rollout with best-effort async processing
 */
export declare function saveRollout(sessionId: string, items: ResponseItem[]): void;
/**
 * Load session by ID
 */
export declare function loadSession(sessionId: string): SessionData | null;
/**
 * List all available sessions
 */
export declare function listSessions(): SessionData[];
/**
 * Delete session by ID
 */
export declare function deleteSession(sessionId: string): boolean;
/**
 * Get session statistics
 */
export declare function getSessionStats(): {
    totalSessions: number;
    totalMessages: number;
    totalDuration: number;
    averageSessionLength: number;
    oldestSession?: Date;
    newestSession?: Date;
};
/**
 * Export session to file
 */
export declare function exportSession(sessionId: string, outputPath: string): boolean;
/**
 * Import session from file
 */
export declare function importSession(inputPath: string): string | null;
/**
 * Search sessions by content
 */
export declare function searchSessions(query: string): SessionData[];
//# sourceMappingURL=save-rollout.d.ts.map