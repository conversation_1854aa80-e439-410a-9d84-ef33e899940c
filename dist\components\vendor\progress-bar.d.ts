/**
 * Progress Bar Component
 *
 * Provides customizable progress bars with percentage display,
 * different styles, and smooth animations.
 */
import blessed from 'blessed';
export interface ProgressBarOptions {
    parent: blessed.Widgets.Node;
    top?: string | number;
    left?: string | number;
    width?: string | number;
    height?: string | number;
    style?: any;
    barStyle?: any;
    showPercentage?: boolean;
    showValue?: boolean;
    min?: number;
    max?: number;
    value?: number;
    orientation?: 'horizontal' | 'vertical';
    char?: string;
    emptyChar?: string;
    border?: boolean;
    label?: string;
}
export declare class ProgressBar {
    private element;
    private barElement;
    private labelElement?;
    private min;
    private max;
    private currentValue;
    private showPercentage;
    private showValue;
    private orientation;
    private char;
    private emptyChar;
    constructor(options: ProgressBarOptions);
    /**
     * Set progress value
     */
    setValue(value: number): void;
    /**
     * Get current value
     */
    getValue(): number;
    /**
     * Set minimum value
     */
    setMin(min: number): void;
    /**
     * Set maximum value
     */
    setMax(max: number): void;
    /**
     * Increment progress
     */
    increment(amount?: number): void;
    /**
     * Decrement progress
     */
    decrement(amount?: number): void;
    /**
     * Get progress percentage
     */
    getPercentage(): number;
    /**
     * Check if progress is complete
     */
    isComplete(): boolean;
    /**
     * Reset progress to minimum
     */
    reset(): void;
    /**
     * Set progress to maximum
     */
    complete(): void;
    /**
     * Update the visual display
     */
    private updateDisplay;
    /**
     * Set label text
     */
    setLabel(label: string): void;
    /**
     * Animate progress to target value
     */
    animateTo(targetValue: number, duration?: number): Promise<void>;
    /**
     * Get the underlying blessed element
     */
    getElement(): blessed.Widgets.BoxElement;
    /**
     * Show the progress bar
     */
    show(): void;
    /**
     * Hide the progress bar
     */
    hide(): void;
    /**
     * Destroy the progress bar
     */
    destroy(): void;
}
/**
 * Create a simple progress bar
 */
export declare function createSimpleProgressBar(parent: blessed.Widgets.Node, options?: Partial<ProgressBarOptions>): ProgressBar;
/**
 * Create a loading progress bar
 */
export declare function createLoadingProgressBar(parent: blessed.Widgets.Node, label: string, position?: {
    top: string | number;
    left: string | number;
}): ProgressBar;
export default ProgressBar;
//# sourceMappingURL=progress-bar.d.ts.map