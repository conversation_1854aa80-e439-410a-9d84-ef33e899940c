/**
 * Single-Pass Mode Implementation
 *
 * Experimental full-context mode that loads entire codebase
 * and applies multiple changes in one pass.
 */
import type { AppConfig } from './types/index.js';
export interface SinglePassConfig {
    originalPrompt: string;
    config: AppConfig;
    rootPath: string;
    maxFiles?: number;
    maxTotalSize?: number;
    excludePatterns?: string[];
}
/**
 * Run single-pass mode with full context
 */
export declare function runSinglePass(options: SinglePassConfig): Promise<void>;
/**
 * Validate single-pass configuration
 */
export declare function validateSinglePassConfig(config: SinglePassConfig): string[];
//# sourceMappingURL=cli-singlepass.d.ts.map