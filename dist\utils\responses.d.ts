/**
 * Response Processing System
 *
 * Handles real-time streaming responses from AI providers,
 * processes function calls, and manages conversation state.
 */
import type { OpenAI } from 'openai';
import type { ResponseEvent, ResponseInputItem, ResponseOutputItem, ResponseFunctionToolCall } from '../types/index.js';
/**
 * Response creation input
 */
export interface ResponseCreateInput {
    messages: ResponseInputItem[];
    model: string;
    provider: string;
    maxTokens?: number;
    temperature?: number;
    tools?: Array<{
        type: 'function';
        function: {
            name: string;
            description: string;
            parameters: any;
        };
    }>;
    stream?: boolean;
}
/**
 * Stream responses from OpenAI-compatible API
 */
export declare function streamResponses(input: ResponseCreateInput, completion: AsyncIterable<OpenAI.ChatCompletionChunk>): AsyncGenerator<ResponseEvent>;
/**
 * Create non-streaming response
 */
export declare function createResponse(input: ResponseCreateInput, client: OpenAI): Promise<ResponseOutputItem>;
/**
 * Convert OpenAI messages to internal format
 */
export declare function convertToInternalMessages(messages: Array<{
    role: string;
    content: string | Array<any>;
}>): ResponseInputItem[];
/**
 * Convert internal messages to OpenAI format
 */
export declare function convertToOpenAIMessages(messages: ResponseInputItem[]): Array<{
    role: string;
    content: string | Array<any>;
}>;
/**
 * Extract function calls from response
 */
export declare function extractFunctionCalls(response: ResponseOutputItem): ResponseFunctionToolCall[];
/**
 * Extract text content from response
 */
export declare function extractTextContent(response: ResponseOutputItem): string;
/**
 * Calculate response statistics
 */
export declare function calculateResponseStats(response: ResponseOutputItem): {
    textLength: number;
    functionCalls: number;
    estimatedTokens: number;
    duration?: number;
};
//# sourceMappingURL=responses.d.ts.map