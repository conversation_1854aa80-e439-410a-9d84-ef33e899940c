/**
 * Agent Loop Core
 *
 * The heart of the AI system implementing sophisticated autonomous workflow
 * with tool calling, conversation state management, and streaming responses.
 */
import { EventEmitter } from 'events';
import type { AgentLoopConfig, ResponseInputItem, ResponseOutputItem, ApprovalPolicy } from '../../types/index.js';
/**
 * Agent Loop implementation
 */
export declare class Agent<PERSON>oop extends EventEmitter {
    private model;
    private provider;
    private approvalPolicy;
    private client;
    private transcript;
    private pendingAborts;
    private cumulativeThinkingMs;
    private maxIterations;
    private timeout;
    private debug;
    constructor(config: AgentLoopConfig);
    /**
     * Process user input through the agent loop
     */
    processInput(input: ResponseInputItem): Promise<ResponseOutputItem>;
    /**
     * Process streaming response
     */
    private processStreamingResponse;
    /**
     * Build conversation context from transcript
     */
    private buildConversationContext;
    /**
     * Get available tools based on approval policy
     */
    private getAvailableTools;
    /**
     * Check if response has function calls
     */
    private hasFunctionCalls;
    /**
     * Process function calls in response
     */
    private processFunctionCalls;
    /**
     * Handle individual function call
     */
    private handleFunctionCall;
    /**
     * Handle shell command execution
     */
    private handleShellCommand;
    /**
     * Add response to transcript
     */
    private addResponseToTranscript;
    /**
     * Update model configuration
     */
    updateModel(model: string, provider?: string): void;
    /**
     * Update approval policy
     */
    updateApprovalPolicy(policy: ApprovalPolicy): void;
    /**
     * Update configuration
     */
    updateConfig(config: Partial<AgentLoopConfig>): void;
    /**
     * Clear conversation transcript
     */
    clearTranscript(): void;
    /**
     * Get conversation statistics
     */
    getStats(): {
        messageCount: number;
        thinkingTime: number;
        modelInfo: {
            model: string;
            provider: string;
        };
    };
    /**
     * Abort pending operations
     */
    abort(): void;
}
//# sourceMappingURL=agent-loop.d.ts.map