/**
 * Error handling utilities
 *
 * Provides safe error handling for unknown error types
 */
/**
 * Safely convert unknown error to Error object
 */
export function toError(error) {
    if (error instanceof Error) {
        return error;
    }
    if (typeof error === 'string') {
        return new Error(error);
    }
    if (error && typeof error === 'object' && 'message' in error) {
        return new Error(String(error.message));
    }
    return new Error('Unknown error occurred');
}
/**
 * Safely get error message from unknown error
 */
export function getErrorMessage(error) {
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    if (error && typeof error === 'object' && 'message' in error) {
        return String(error.message);
    }
    return 'Unknown error occurred';
}
/**
 * Safely get error status code from unknown error
 */
export function getErrorStatus(error) {
    if (error && typeof error === 'object' && 'status' in error) {
        const status = error.status;
        return typeof status === 'number' ? status : undefined;
    }
    return undefined;
}
/**
 * Check if error has a specific property
 */
export function hasErrorProperty(error, property) {
    return error && typeof error === 'object' && property in error;
}
/**
 * Safely get error property value
 */
export function getErrorProperty(error, property) {
    if (hasErrorProperty(error, property)) {
        return error[property];
    }
    return undefined;
}
/**
 * Create a standardized error object with additional context
 */
export function createError(message, originalError, context) {
    const error = new Error(message);
    if (originalError) {
        error.originalError = originalError;
        error.originalMessage = getErrorMessage(originalError);
    }
    if (context) {
        Object.assign(error, context);
    }
    return error;
}
/**
 * Wrap async function with error handling
 */
export function withErrorHandling(fn, errorHandler) {
    return async (...args) => {
        try {
            return await fn(...args);
        }
        catch (error) {
            if (errorHandler) {
                errorHandler(error);
            }
            return undefined;
        }
    };
}
/**
 * Wrap sync function with error handling
 */
export function withSyncErrorHandling(fn, errorHandler) {
    return (...args) => {
        try {
            return fn(...args);
        }
        catch (error) {
            if (errorHandler) {
                errorHandler(error);
            }
            return undefined;
        }
    };
}
//# sourceMappingURL=error-utils.js.map