/**
 * Simple state management hook for terminal UI components
 * Provides React-like useState functionality for blessed.js components
 */
/**
 * Create a state hook with getter and setter
 */
export function useState(initialValue) {
    let currentValue = initialValue;
    const listeners = new Set();
    const getValue = () => currentValue;
    const setValue = (value) => {
        const newValue = typeof value === 'function'
            ? value(currentValue)
            : value;
        if (newValue !== currentValue) {
            currentValue = newValue;
            // Notify all listeners
            listeners.forEach(listener => listener());
        }
    };
    const subscribe = (listener) => {
        listeners.add(listener);
        return () => listeners.delete(listener);
    };
    // Attach subscribe method to getValue for component integration
    getValue.subscribe = subscribe;
    return [getValue, setValue];
}
/**
 * Create a state manager for complex state objects
 */
export class StateManager {
    state;
    listeners = new Set();
    constructor(initialState) {
        this.state = { ...initialState };
    }
    getState() {
        return { ...this.state };
    }
    setState(updates) {
        const newUpdates = typeof updates === 'function'
            ? updates(this.state)
            : updates;
        const newState = { ...this.state, ...newUpdates };
        // Check if state actually changed
        if (JSON.stringify(newState) !== JSON.stringify(this.state)) {
            this.state = newState;
            this.notifyListeners();
        }
    }
    subscribe(listener) {
        this.listeners.add(listener);
        return () => this.listeners.delete(listener);
    }
    notifyListeners() {
        this.listeners.forEach(listener => listener(this.state));
    }
}
/**
 * Create a reactive state hook that can trigger re-renders
 */
export function useReactiveState(initialValue, onUpdate) {
    const [getValue, setValue] = useState(initialValue);
    const reactiveSetValue = (value) => {
        setValue(value);
        if (onUpdate) {
            onUpdate(getValue());
        }
    };
    return [getValue, reactiveSetValue];
}
/**
 * Create a computed state that depends on other state values
 */
export function useComputed(dependencies, computeFn) {
    let cachedValue;
    let lastDeps;
    return () => {
        // Check if dependencies changed
        if (!lastDeps || dependencies.some((dep, i) => dep !== lastDeps[i])) {
            cachedValue = computeFn(...dependencies);
            lastDeps = [...dependencies];
        }
        return cachedValue;
    };
}
/**
 * Create a state hook with persistence to localStorage-like storage
 */
export function usePersistedState(key, initialValue, storage) {
    const defaultStorage = {
        getItem: (key) => {
            try {
                // Simple file-based storage for CLI
                const fs = require('fs');
                const path = require('path');
                const os = require('os');
                const storageDir = path.join(os.homedir(), '.kritrima-ai', 'state');
                const filePath = path.join(storageDir, `${key}.json`);
                if (fs.existsSync(filePath)) {
                    return fs.readFileSync(filePath, 'utf8');
                }
                return null;
            }
            catch {
                return null;
            }
        },
        setItem: (key, value) => {
            try {
                const fs = require('fs');
                const path = require('path');
                const os = require('os');
                const storageDir = path.join(os.homedir(), '.kritrima-ai', 'state');
                // Ensure directory exists
                fs.mkdirSync(storageDir, { recursive: true });
                const filePath = path.join(storageDir, `${key}.json`);
                fs.writeFileSync(filePath, value, 'utf8');
            }
            catch {
                // Ignore storage errors
            }
        },
    };
    const store = storage || defaultStorage;
    // Load initial value from storage
    let storedValue;
    try {
        const stored = store.getItem(key);
        storedValue = stored ? JSON.parse(stored) : initialValue;
    }
    catch {
        storedValue = initialValue;
    }
    const [getValue, setValueInternal] = useState(storedValue);
    const setValue = (value) => {
        setValueInternal(value);
        // Persist to storage
        try {
            const newValue = typeof value === 'function'
                ? value(getValue())
                : value;
            store.setItem(key, JSON.stringify(newValue));
        }
        catch {
            // Ignore storage errors
        }
    };
    return [getValue, setValue];
}
/**
 * Create a debounced state hook that delays updates
 */
export function useDebouncedState(initialValue, delay = 300) {
    const [getValue, setValue] = useState(initialValue);
    const [getDebouncedValue, setDebouncedValue] = useState(initialValue);
    let timeoutId = null;
    const setValueWithDebounce = (value) => {
        setValue(value);
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
            setDebouncedValue(getValue());
        }, delay);
    };
    return [getValue, setValueWithDebounce, getDebouncedValue];
}
//# sourceMappingURL=use-state.js.map