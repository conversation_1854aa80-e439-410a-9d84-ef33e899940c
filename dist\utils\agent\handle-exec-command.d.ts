/**
 * Shell Command Execution Handler
 *
 * Handles command processing pipeline with platform adaptation,
 * security validation, and result formatting.
 */
import type { ExecInput, ExecResult, ApprovalPolicy } from '../../types/index.js';
export interface ExecConfig {
    approvalPolicy: ApprovalPolicy;
    workingDirectory?: string;
    additionalWritableRoots?: string[];
    safeCommands?: string[];
    dangerousCommands?: string[];
}
/**
 * Handle command execution with full pipeline processing
 */
export declare function handleExecCommand(input: ExecInput, config: ExecConfig): Promise<ExecResult>;
/**
 * Format command result for display
 */
export declare function formatExecResult(result: ExecResult): string;
/**
 * Validate command safety
 */
export declare function validateCommandSafety(command: string[]): {
    safe: boolean;
    warnings: string[];
    blocked: string[];
};
/**
 * Get command execution statistics
 */
export declare function getExecStats(): {
    totalCommands: number;
    successfulCommands: number;
    failedCommands: number;
    averageDuration: number;
    mostUsedCommands: Array<{
        command: string;
        count: number;
    }>;
};
//# sourceMappingURL=handle-exec-command.d.ts.map