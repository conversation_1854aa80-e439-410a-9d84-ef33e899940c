/**
 * Git Repository Detection
 *
 * Fast and reliable Git repository detection using git rev-parse.
 * Works across different Git versions and configurations.
 */
/**
 * Check if directory is inside a Git repository
 */
export declare function checkInGit(workdir: string): boolean;
/**
 * Get Git repository root directory
 */
export declare function getGitRoot(workdir: string): string | null;
/**
 * Check if directory has uncommitted changes
 */
export declare function hasUncommittedChanges(workdir: string): boolean;
/**
 * Get current Git branch name
 */
export declare function getCurrentBranch(workdir: string): string | null;
/**
 * Get Git repository status information
 */
export declare function getGitStatus(workdir: string): {
    isGitRepo: boolean;
    root?: string;
    branch?: string;
    hasChanges?: boolean;
    ahead?: number;
    behind?: number;
};
/**
 * Check if file is tracked by Git
 */
export declare function isFileTracked(filePath: string, workdir: string): boolean;
/**
 * Check if file is ignored by Git
 */
export declare function isFileIgnored(filePath: string, workdir: string): boolean;
/**
 * Get list of modified files
 */
export declare function getModifiedFiles(workdir: string): string[];
/**
 * Get list of staged files
 */
export declare function getStagedFiles(workdir: string): string[];
/**
 * Get list of untracked files
 */
export declare function getUntrackedFiles(workdir: string): string[];
/**
 * Get comprehensive Git file status
 */
export declare function getFileStatus(workdir: string): {
    modified: string[];
    staged: string[];
    untracked: string[];
    total: number;
};
/**
 * Validate Git installation
 */
export declare function validateGitInstallation(): boolean;
/**
 * Get Git version
 */
export declare function getGitVersion(): string | null;
//# sourceMappingURL=check-in-git.d.ts.map