/**
 * Advanced Patch Application System
 *
 * Handles unified diff processing, V4A diff format support,
 * conflict resolution, and file operations with backup/rollback.
 */
export interface PatchResult {
    success: boolean;
    filesModified: string[];
    filesCreated: string[];
    filesDeleted: string[];
    conflicts: string[];
    errors: string[];
    backupPaths: string[];
}
/**
 * Apply patch with comprehensive error handling and backup
 */
export declare function applyPatch(patchContent: string, workingDirectory?: string, createBackup?: boolean): Promise<PatchResult>;
/**
 * Rollback patch using backup files
 */
export declare function rollbackPatch(backupPaths: string[]): Promise<boolean>;
//# sourceMappingURL=apply-patch.d.ts.map