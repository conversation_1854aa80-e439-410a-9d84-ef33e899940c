/**
 * Input Processing Utilities
 *
 * Handles text and image input processing for multi-modal AI interactions,
 * including file tag expansion and image optimization.
 */
import type { ResponseInputItem, ResponseContentInput } from '../types/index.js';
/**
 * Create input item from text and image paths
 */
export declare function createInputItem(text: string, imagePaths?: string[]): Promise<ResponseInputItem>;
/**
 * Process image file for AI consumption
 */
export declare function processImage(imagePath: string): Promise<{
    url?: string;
    data?: string;
    mimeType?: string;
}>;
/**
 * Expand file tags (@file.txt) in text to XML blocks with file contents
 */
export declare function expandFileTags(text: string): Promise<string>;
/**
 * Collapse XML blocks back to file tags
 */
export declare function collapseXmlBlocks(text: string): string;
/**
 * Extract file paths from text (both @file.txt and XML blocks)
 */
export declare function extractFilePaths(text: string): string[];
/**
 * Validate input content
 */
export declare function validateInputContent(content: ResponseContentInput[]): string[];
/**
 * Get content statistics
 */
export declare function getContentStats(content: ResponseContentInput[]): {
    textItems: number;
    imageItems: number;
    totalTextLength: number;
    estimatedTokens: number;
};
//# sourceMappingURL=input-utils.d.ts.map