/**
 * Advanced Patch Application System
 *
 * Handles unified diff processing, V4A diff format support,
 * conflict resolution, and file operations with backup/rollback.
 */
import { readFileSync, writeFileSync, existsSync, mkdirSync, copyFileSync, unlinkSync } from 'fs';
import { dirname, join, resolve } from 'path';
import { logInfo, logError, logWarn, logDebug, toError } from '../logger/log.js';
/**
 * Apply patch with comprehensive error handling and backup
 */
export async function applyPatch(patchContent, workingDirectory = process.cwd(), createBackup = true) {
    const result = {
        success: false,
        filesModified: [],
        filesCreated: [],
        filesDeleted: [],
        conflicts: [],
        errors: [],
        backupPaths: [],
    };
    try {
        logInfo('Applying patch...');
        // Parse patch content
        const operations = parsePatch(patchContent);
        if (operations.length === 0) {
            result.errors.push('No valid patch operations found');
            return result;
        }
        // Create backup directory if needed
        const backupDir = createBackup ? createBackupDirectory(workingDirectory) : null;
        // Apply each operation
        for (const operation of operations) {
            try {
                await applyOperation(operation, workingDirectory, backupDir, result);
            }
            catch (error) {
                const err = toError(error);
                result.errors.push(`Failed to apply operation on ${operation.path}: ${err.message}`);
                logError(`Patch operation failed: ${operation.path}`, err);
            }
        }
        result.success = result.errors.length === 0;
        if (result.success) {
            logInfo(`Patch applied successfully: ${operations.length} operations`);
        }
        else {
            logError(`Patch application failed: ${result.errors.length} errors`);
        }
        return result;
    }
    catch (error) {
        const err = toError(error);
        logError('Patch application failed', err);
        result.errors.push(`Patch application failed: ${err.message}`);
        return result;
    }
}
/**
 * Parse patch content into operations
 */
function parsePatch(patchContent) {
    const operations = [];
    // Handle different patch formats
    if (patchContent.includes('*** [') && patchContent.includes('] File:')) {
        // V4A diff format
        operations.push(...parseV4ADiff(patchContent));
    }
    else if (patchContent.includes('diff --git') || patchContent.includes('--- ')) {
        // Unified diff format
        operations.push(...parseUnifiedDiff(patchContent));
    }
    else {
        // Try to parse as simple file content
        operations.push(...parseSimpleContent(patchContent));
    }
    return operations;
}
/**
 * Parse V4A diff format
 */
function parseV4ADiff(content) {
    const operations = [];
    const fileBlocks = content.split(/\*\*\* \[([^\]]+)\] File: ([^\n]+)/);
    for (let i = 1; i < fileBlocks.length; i += 3) {
        const action = fileBlocks[i].trim();
        const filePath = fileBlocks[i + 1].trim();
        const diffContent = fileBlocks[i + 2];
        switch (action.toUpperCase()) {
            case 'CREATE':
                operations.push({
                    type: 'create',
                    path: filePath,
                    content: extractFileContent(diffContent),
                });
                break;
            case 'UPDATE':
                operations.push({
                    type: 'update',
                    path: filePath,
                    content: applyDiffToContent(filePath, diffContent),
                });
                break;
            case 'DELETE':
                operations.push({
                    type: 'delete',
                    path: filePath,
                });
                break;
            case 'MOVE':
                const newPath = extractNewPath(diffContent);
                operations.push({
                    type: 'move',
                    path: filePath,
                    newPath,
                });
                break;
        }
    }
    return operations;
}
/**
 * Parse unified diff format
 */
function parseUnifiedDiff(content) {
    const operations = [];
    const files = content.split(/^diff --git /m);
    for (const fileContent of files) {
        if (!fileContent.trim()) {
            continue;
        }
        const lines = fileContent.split('\n');
        let filePath = '';
        let isNewFile = false;
        let isDeletedFile = false;
        // Extract file path
        for (const line of lines) {
            if (line.startsWith('--- ')) {
                const match = line.match(/--- a\/(.+)/) || line.match(/--- (.+)/);
                if (match) {
                    filePath = match[1];
                }
            }
            else if (line.startsWith('+++ ')) {
                const match = line.match(/\+\+\+ b\/(.+)/) || line.match(/\+\+\+ (.+)/);
                if (match) {
                    filePath = match[1];
                }
            }
            else if (line.includes('new file mode')) {
                isNewFile = true;
            }
            else if (line.includes('deleted file mode')) {
                isDeletedFile = true;
            }
        }
        if (!filePath) {
            continue;
        }
        if (isDeletedFile) {
            operations.push({ type: 'delete', path: filePath });
        }
        else if (isNewFile) {
            const content = extractNewFileContent(lines);
            operations.push({ type: 'create', path: filePath, content });
        }
        else {
            const content = applyUnifiedDiff(filePath, lines);
            operations.push({ type: 'update', path: filePath, content });
        }
    }
    return operations;
}
/**
 * Parse simple content format
 */
function parseSimpleContent(content) {
    // Try to extract file operations from simple text
    const operations = [];
    // Look for file creation patterns
    const createPattern = /(?:create|new)\s+file[:\s]+([^\n]+)\n([\s\S]*?)(?=\n(?:create|new|update|delete)|$)/gi;
    let match;
    while ((match = createPattern.exec(content)) !== null) {
        operations.push({
            type: 'create',
            path: match[1].trim(),
            content: match[2].trim(),
        });
    }
    return operations;
}
/**
 * Apply single operation
 */
async function applyOperation(operation, workingDirectory, backupDir, result) {
    const fullPath = resolve(workingDirectory, operation.path);
    switch (operation.type) {
        case 'create':
            await createFile(fullPath, operation.content || '', result);
            break;
        case 'update':
            await updateFile(fullPath, operation.content || '', backupDir, result);
            break;
        case 'delete':
            await deleteFile(fullPath, backupDir, result);
            break;
        case 'move':
            await moveFile(fullPath, operation.newPath, backupDir, result);
            break;
    }
}
/**
 * Create new file
 */
async function createFile(filePath, content, result) {
    if (existsSync(filePath)) {
        throw new Error(`File already exists: ${filePath}`);
    }
    // Ensure directory exists
    const dir = dirname(filePath);
    if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
    }
    writeFileSync(filePath, content, 'utf8');
    result.filesCreated.push(filePath);
    logDebug(`Created file: ${filePath}`);
}
/**
 * Update existing file
 */
async function updateFile(filePath, content, backupDir, result) {
    if (!existsSync(filePath)) {
        throw new Error(`File does not exist: ${filePath}`);
    }
    // Create backup
    if (backupDir) {
        const backupPath = createBackupFile(filePath, backupDir);
        result.backupPaths.push(backupPath);
    }
    writeFileSync(filePath, content, 'utf8');
    result.filesModified.push(filePath);
    logDebug(`Updated file: ${filePath}`);
}
/**
 * Delete file
 */
async function deleteFile(filePath, backupDir, result) {
    if (!existsSync(filePath)) {
        logWarn(`File does not exist for deletion: ${filePath}`);
        return;
    }
    // Create backup
    if (backupDir) {
        const backupPath = createBackupFile(filePath, backupDir);
        result.backupPaths.push(backupPath);
    }
    unlinkSync(filePath);
    result.filesDeleted.push(filePath);
    logDebug(`Deleted file: ${filePath}`);
}
/**
 * Move/rename file
 */
async function moveFile(oldPath, newPath, backupDir, result) {
    if (!existsSync(oldPath)) {
        throw new Error(`Source file does not exist: ${oldPath}`);
    }
    const fullNewPath = resolve(dirname(oldPath), newPath);
    // Create backup
    if (backupDir) {
        const backupPath = createBackupFile(oldPath, backupDir);
        result.backupPaths.push(backupPath);
    }
    // Ensure target directory exists
    const targetDir = dirname(fullNewPath);
    if (!existsSync(targetDir)) {
        mkdirSync(targetDir, { recursive: true });
    }
    // Copy and delete (safer than rename across filesystems)
    copyFileSync(oldPath, fullNewPath);
    unlinkSync(oldPath);
    result.filesDeleted.push(oldPath);
    result.filesCreated.push(fullNewPath);
    logDebug(`Moved file: ${oldPath} -> ${fullNewPath}`);
}
/**
 * Create backup directory
 */
function createBackupDirectory(workingDirectory) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = join(workingDirectory, '.kritrima-ai-backups', timestamp);
    if (!existsSync(backupDir)) {
        mkdirSync(backupDir, { recursive: true });
    }
    return backupDir;
}
/**
 * Create backup file
 */
function createBackupFile(filePath, backupDir) {
    const relativePath = filePath.replace(process.cwd(), '').replace(/^[/\\]/, '');
    const backupPath = join(backupDir, relativePath);
    // Ensure backup directory exists
    const backupFileDir = dirname(backupPath);
    if (!existsSync(backupFileDir)) {
        mkdirSync(backupFileDir, { recursive: true });
    }
    copyFileSync(filePath, backupPath);
    return backupPath;
}
/**
 * Extract file content from diff
 */
function extractFileContent(diffContent) {
    // Remove diff markers and extract content
    const lines = diffContent.split('\n');
    const contentLines = [];
    for (const line of lines) {
        if (line.startsWith('+') && !line.startsWith('+++')) {
            contentLines.push(line.substring(1));
        }
        else if (!line.startsWith('-') && !line.startsWith('@@') && !line.startsWith('\\')) {
            contentLines.push(line);
        }
    }
    return contentLines.join('\n');
}
/**
 * Apply diff to existing content
 */
function applyDiffToContent(filePath, diffContent) {
    if (!existsSync(filePath)) {
        return extractFileContent(diffContent);
    }
    const originalContent = readFileSync(filePath, 'utf8');
    const lines = originalContent.split('\n');
    const diffLines = diffContent.split('\n');
    // Simple line-by-line diff application
    // This is a simplified implementation - a full implementation would handle
    // context lines, line numbers, and more complex diff formats
    const result = [];
    let lineIndex = 0;
    for (const diffLine of diffLines) {
        if (diffLine.startsWith('+') && !diffLine.startsWith('+++')) {
            result.push(diffLine.substring(1));
        }
        else if (diffLine.startsWith('-') && !diffLine.startsWith('---')) {
            // Skip deleted lines
            lineIndex++;
        }
        else if (!diffLine.startsWith('@@') && !diffLine.startsWith('\\')) {
            if (lineIndex < lines.length) {
                result.push(lines[lineIndex]);
                lineIndex++;
            }
        }
    }
    return result.join('\n');
}
/**
 * Extract new file content from unified diff
 */
function extractNewFileContent(lines) {
    const contentLines = [];
    for (const line of lines) {
        if (line.startsWith('+') && !line.startsWith('+++')) {
            contentLines.push(line.substring(1));
        }
    }
    return contentLines.join('\n');
}
/**
 * Apply unified diff to existing file
 */
function applyUnifiedDiff(filePath, diffLines) {
    if (!existsSync(filePath)) {
        return extractNewFileContent(diffLines);
    }
    const originalContent = readFileSync(filePath, 'utf8');
    // Simplified diff application - would need more sophisticated implementation
    // for production use with proper context matching and conflict resolution
    return originalContent;
}
/**
 * Extract new path from move operation
 */
function extractNewPath(content) {
    const lines = content.split('\n');
    for (const line of lines) {
        if (line.includes('->') || line.includes('renamed to')) {
            const match = line.match(/(?:->|renamed to)\s*(.+)$/);
            if (match) {
                return match[1].trim();
            }
        }
    }
    return '';
}
/**
 * Rollback patch using backup files
 */
export async function rollbackPatch(backupPaths) {
    try {
        logInfo('Rolling back patch...');
        for (const backupPath of backupPaths) {
            // Restore from backup
            const originalPath = backupPath.replace(/.kritrima-ai-backups[/\\][^/\\]+[/\\]/, '');
            if (existsSync(backupPath)) {
                copyFileSync(backupPath, originalPath);
                logDebug(`Restored: ${originalPath}`);
            }
        }
        logInfo('Patch rollback completed');
        return true;
    }
    catch (error) {
        const err = toError(error);
        logError('Patch rollback failed', err);
        return false;
    }
}
//# sourceMappingURL=apply-patch.js.map