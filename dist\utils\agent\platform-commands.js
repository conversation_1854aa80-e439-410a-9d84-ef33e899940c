/**
 * Platform Command Adaptation
 *
 * Converts Unix commands to Windows equivalents and handles
 * cross-platform command compatibility.
 */
import { logDebug } from '../logger/log.js';
/**
 * Command mapping from Unix to Windows
 */
const COMMAND_MAP = {
    // File operations
    'ls': 'dir',
    'cat': 'type',
    'cp': 'copy',
    'mv': 'move',
    'rm': 'del',
    'mkdir': 'mkdir',
    'rmdir': 'rmdir',
    'touch': 'echo. >',
    // Text processing
    'grep': 'findstr',
    'head': 'more',
    'tail': 'more',
    'wc': 'find /c /v ""',
    'sort': 'sort',
    // System info
    'ps': 'tasklist',
    'kill': 'taskkill',
    'which': 'where',
    'whoami': 'whoami',
    'pwd': 'cd',
    // Network
    'curl': 'curl',
    'wget': 'curl',
    'ping': 'ping',
    'netstat': 'netstat',
    // Archive
    'tar': '7z',
    'unzip': '7z x',
    'zip': '7z a',
};
/**
 * Flag mapping from Unix to Windows
 */
const FLAG_MAP = {
    'ls': {
        '-l': '/w',
        '-la': '/a',
        '-a': '/a',
        '-h': '',
        '-R': '/s',
    },
    'rm': {
        '-f': '/f',
        '-r': '/s',
        '-rf': '/s /f',
    },
    'cp': {
        '-r': '/s',
        '-f': '/y',
    },
    'grep': {
        '-i': '/i',
        '-n': '/n',
        '-r': '/s',
        '-v': '/v',
    },
    'ps': {
        'aux': '/v',
        '-ef': '/v',
    },
    'kill': {
        '-9': '/f',
        '-f': '/f',
    },
};
/**
 * Adapt command for current platform
 */
export function adaptCommandForPlatform(command) {
    if (process.platform !== 'win32') {
        // On Unix-like systems, return command as-is
        return command;
    }
    // Adapt for Windows
    return adaptForWindows(command);
}
/**
 * Adapt Unix command for Windows
 */
function adaptForWindows(command) {
    if (command.length === 0) {
        return command;
    }
    const [cmd, ...args] = command;
    const lowerCmd = cmd.toLowerCase();
    // Check if command needs adaptation
    if (!COMMAND_MAP[lowerCmd]) {
        // Command doesn't need adaptation
        return command;
    }
    const windowsCmd = COMMAND_MAP[lowerCmd];
    const adaptedArgs = adaptArgsForWindows(lowerCmd, args);
    logDebug(`Adapted command: ${cmd} -> ${windowsCmd}`);
    // Handle special cases
    switch (lowerCmd) {
        case 'touch':
            return handleTouchCommand(adaptedArgs);
        case 'wget':
            return handleWgetCommand(adaptedArgs);
        case 'tar':
            return handleTarCommand(adaptedArgs);
        case 'grep':
            return handleGrepCommand(adaptedArgs);
        default:
            return [windowsCmd, ...adaptedArgs];
    }
}
/**
 * Adapt arguments for Windows
 */
function adaptArgsForWindows(command, args) {
    const flagMap = FLAG_MAP[command];
    if (!flagMap) {
        return args;
    }
    const adaptedArgs = [];
    let i = 0;
    while (i < args.length) {
        const arg = args[i];
        if (arg.startsWith('-')) {
            // Check for flag mapping
            const windowsFlag = flagMap[arg];
            if (windowsFlag !== undefined) {
                if (windowsFlag) {
                    adaptedArgs.push(...windowsFlag.split(' '));
                }
                // Skip empty mappings (flags that don't exist on Windows)
            }
            else {
                // Keep unmapped flags as-is
                adaptedArgs.push(arg);
            }
        }
        else {
            // Regular argument
            adaptedArgs.push(adaptPathForWindows(arg));
        }
        i++;
    }
    return adaptedArgs;
}
/**
 * Adapt file paths for Windows
 */
function adaptPathForWindows(path) {
    // Convert forward slashes to backslashes
    let windowsPath = path.replace(/\//g, '\\');
    // Handle special Unix paths
    if (windowsPath.startsWith('~')) {
        windowsPath = windowsPath.replace('~', '%USERPROFILE%');
    }
    // Handle /dev/null
    if (windowsPath === '\\dev\\null') {
        windowsPath = 'NUL';
    }
    return windowsPath;
}
/**
 * Handle touch command (create empty file)
 */
function handleTouchCommand(args) {
    if (args.length === 0) {
        return ['echo', '.', '>', 'NUL'];
    }
    const filename = args[args.length - 1];
    return ['echo', '.', '>', filename];
}
/**
 * Handle wget command (use curl instead)
 */
function handleWgetCommand(args) {
    const curlArgs = ['curl'];
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        switch (arg) {
            case '-O':
                curlArgs.push('-O');
                break;
            case '-o':
                curlArgs.push('-o');
                if (i + 1 < args.length) {
                    curlArgs.push(args[++i]);
                }
                break;
            case '--quiet':
            case '-q':
                curlArgs.push('-s');
                break;
            default:
                curlArgs.push(arg);
        }
    }
    return curlArgs;
}
/**
 * Handle tar command (use 7z instead)
 */
function handleTarCommand(args) {
    const sevenZipArgs = ['7z'];
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        if (arg.startsWith('-')) {
            if (arg.includes('x')) {
                sevenZipArgs.push('x');
            }
            else if (arg.includes('c')) {
                sevenZipArgs.push('a');
            }
            else if (arg.includes('t')) {
                sevenZipArgs.push('t');
            }
        }
        else {
            sevenZipArgs.push(arg);
        }
    }
    return sevenZipArgs;
}
/**
 * Handle grep command (use findstr)
 */
function handleGrepCommand(args) {
    const findstrArgs = ['findstr'];
    let pattern = '';
    const files = [];
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        if (arg.startsWith('-')) {
            // Flags are handled in adaptArgsForWindows
            continue;
        }
        else if (!pattern) {
            pattern = arg;
        }
        else {
            files.push(arg);
        }
    }
    if (pattern) {
        findstrArgs.push(pattern);
    }
    findstrArgs.push(...files);
    return findstrArgs;
}
/**
 * Check if command is available on current platform
 */
export function isCommandAvailable(command) {
    const { execSync } = require('child_process');
    try {
        const checkCommand = process.platform === 'win32' ? 'where' : 'which';
        execSync(`${checkCommand} ${command}`, { stdio: 'pipe' });
        return true;
    }
    catch (error) {
        return false;
    }
}
/**
 * Get platform-specific command equivalent
 */
export function getPlatformCommand(unixCommand) {
    if (process.platform !== 'win32') {
        return unixCommand;
    }
    return COMMAND_MAP[unixCommand.toLowerCase()] || unixCommand;
}
/**
 * Validate command for current platform
 */
export function validatePlatformCommand(command) {
    const warnings = [];
    let adapted = [...command];
    if (command.length === 0) {
        return { valid: false, adapted, warnings: ['Empty command'] };
    }
    // Adapt for platform
    adapted = adaptCommandForPlatform(command);
    // Check if adapted command is available
    const commandName = adapted[0];
    if (!isCommandAvailable(commandName)) {
        warnings.push(`Command not available: ${commandName}`);
    }
    // Platform-specific warnings
    if (process.platform === 'win32') {
        // Windows-specific warnings
        if (command[0] === 'sudo') {
            warnings.push('sudo is not available on Windows. Run as Administrator instead.');
        }
        if (command.includes('&&') || command.includes('||')) {
            warnings.push('Shell operators may not work as expected. Consider using separate commands.');
        }
    }
    return {
        valid: warnings.length === 0,
        adapted,
        warnings,
    };
}
/**
 * Get shell for current platform
 */
export function getPlatformShell() {
    switch (process.platform) {
        case 'win32':
            return process.env.COMSPEC || 'cmd.exe';
        case 'darwin':
        case 'linux':
        default:
            return process.env.SHELL || '/bin/bash';
    }
}
/**
 * Get shell flags for command execution
 */
export function getShellFlags() {
    switch (process.platform) {
        case 'win32':
            return ['/c'];
        case 'darwin':
        case 'linux':
        default:
            return ['-c'];
    }
}
/**
 * Execute command with platform-appropriate shell
 */
export function wrapCommandForShell(command) {
    const shell = getPlatformShell();
    const flags = getShellFlags();
    const commandString = command.join(' ');
    return [shell, ...flags, commandString];
}
//# sourceMappingURL=platform-commands.js.map