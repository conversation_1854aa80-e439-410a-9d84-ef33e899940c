/**
 * Sessions Overlay Component
 * 
 * Manages saved conversation sessions with search, load, and delete functionality.
 */

import blessed from 'blessed';
import { readdirSync, statSync, readFileSync, unlinkSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import { logDebug, logError } from '../../utils/logger/log.js';
import type { SessionData } from '../../types/index.js';

export interface SessionsOverlayOptions {
  parent: blessed.Widgets.Screen;
  onLoad?: (sessionData: SessionData) => void;
  onClose?: () => void;
}

interface SessionInfo {
  id: string;
  filename: string;
  timestamp: number;
  messageCount: number;
  model: string;
  provider: string;
  duration: number;
}

export class SessionsOverlay {
  private screen: blessed.Widgets.Screen;
  private container: blessed.Widgets.BoxElement;
  private sessionsList: blessed.Widgets.ListElement;
  private detailsBox: blessed.Widgets.BoxElement;
  private searchBox: blessed.Widgets.TextboxElement;
  private statusBar: blessed.Widgets.BoxElement;
  
  private sessions: SessionInfo[] = [];
  private filteredSessions: SessionInfo[] = [];
  private searchTerm = '';
  private options: SessionsOverlayOptions;
  private sessionsDir: string;

  constructor(options: SessionsOverlayOptions) {
    this.options = options;
    this.screen = options.parent;
    this.sessionsDir = join(homedir(), '.codex', 'sessions');
    
    this.loadSessions();
    this.createComponents();
    this.setupEventHandlers();
    this.updateDisplay();
  }

  /**
   * Load available sessions
   */
  private loadSessions(): void {
    try {
      this.sessions = [];
      
      if (!require('fs').existsSync(this.sessionsDir)) {
        return;
      }

      const files = readdirSync(this.sessionsDir);
      
      for (const file of files) {
        if (file.startsWith('rollout-') && file.endsWith('.json')) {
          try {
            const filePath = join(this.sessionsDir, file);
            const stats = statSync(filePath);
            const content = readFileSync(filePath, 'utf-8');
            const sessionData: SessionData = JSON.parse(content);
            
            this.sessions.push({
              id: sessionData.id,
              filename: file,
              timestamp: sessionData.timestamp,
              messageCount: sessionData.metadata.messageCount,
              model: sessionData.metadata.model,
              provider: sessionData.metadata.provider,
              duration: sessionData.metadata.duration,
            });
          } catch (error) {
            logDebug(`Failed to parse session file ${file}`);
          }
        }
      }
      
      // Sort by timestamp (newest first)
      this.sessions.sort((a, b) => b.timestamp - a.timestamp);
      this.filteredSessions = [...this.sessions];
      
    } catch (error) {
      logError('Failed to load sessions', error as Error);
      this.sessions = [];
      this.filteredSessions = [];
    }
  }

  /**
   * Create UI components
   */
  private createComponents(): void {
    // Main container
    this.container = blessed.box({
      parent: this.screen,
      top: 'center',
      left: 'center',
      width: '90%',
      height: '85%',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'cyan',
        },
      },
      label: ' Saved Sessions ',
      tags: true,
      keys: true,
      vi: true,
    });

    // Search box
    this.searchBox = blessed.textbox({
      parent: this.container,
      top: 0,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'yellow',
        },
      },
      label: ' Search Sessions ',
      inputOnFocus: true,
      keys: true,
    });

    // Sessions list
    this.sessionsList = blessed.list({
      parent: this.container,
      top: 3,
      left: 0,
      width: '50%',
      height: '100%-9',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'white',
        },
        selected: {
          bg: 'blue',
        },
      },
      label: ' Sessions ',
      keys: true,
      vi: true,
      scrollable: true,
      mouse: true,
    });

    // Details box
    this.detailsBox = blessed.box({
      parent: this.container,
      top: 3,
      left: '50%',
      width: '50%',
      height: '100%-9',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'green',
        },
      },
      label: ' Session Details ',
      scrollable: true,
      tags: true,
      keys: true,
      vi: true,
    });

    // Status bar
    this.statusBar = blessed.box({
      parent: this.container,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'gray',
        },
      },
      content: 'Enter: Load | Del: Delete | /: Search | R: Refresh | Esc: Close',
      tags: true,
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Global key handlers
    this.container.key(['escape', 'q'], () => {
      this.close();
    });

    this.container.key(['enter'], () => {
      this.loadSelected();
    });

    this.container.key(['/'], () => {
      this.searchBox.focus();
    });

    this.container.key(['r'], () => {
      this.refresh();
    });

    this.container.key(['delete'], () => {
      this.deleteSelected();
    });

    // Search box handlers
    this.searchBox.on('submit', (value: string) => {
      this.searchTerm = value;
      this.filterSessions();
      this.sessionsList.focus();
    });

    this.searchBox.key(['escape'], () => {
      this.searchBox.clearValue();
      this.searchTerm = '';
      this.filterSessions();
      this.sessionsList.focus();
    });

    // Sessions list handlers
    this.sessionsList.on('select', () => {
      this.loadSelected();
    });

    this.sessionsList.key(['j', 'down'], () => {
      this.sessionsList.down(1);
      this.updateDetails();
      this.screen.render();
    });

    this.sessionsList.key(['k', 'up'], () => {
      this.sessionsList.up(1);
      this.updateDetails();
      this.screen.render();
    });

    // Focus management
    this.sessionsList.focus();
  }

  /**
   * Filter sessions based on search term
   */
  private filterSessions(): void {
    if (!this.searchTerm.trim()) {
      this.filteredSessions = [...this.sessions];
    } else {
      const term = this.searchTerm.toLowerCase();
      this.filteredSessions = this.sessions.filter(session =>
        session.model.toLowerCase().includes(term) ||
        session.provider.toLowerCase().includes(term) ||
        new Date(session.timestamp).toLocaleDateString().includes(term),
      );
    }
    
    this.updateDisplay();
  }

  /**
   * Update display
   */
  private updateDisplay(): void {
    this.updateSessionsList();
    this.updateDetails();
  }

  /**
   * Update sessions list
   */
  private updateSessionsList(): void {
    const items = this.filteredSessions.map(session => {
      const date = new Date(session.timestamp).toLocaleDateString();
      const time = new Date(session.timestamp).toLocaleTimeString();
      return `${date} ${time} - ${session.model} (${session.messageCount} msgs)`;
    });

    this.sessionsList.setItems(items.length > 0 ? items : ['No sessions found']);
    this.screen.render();
  }

  /**
   * Update details display
   */
  private updateDetails(): void {
    const selected = (this.sessionsList as any).selected;
    
    if (selected >= 0 && selected < this.filteredSessions.length) {
      const session = this.filteredSessions[selected];
      const date = new Date(session.timestamp);
      const duration = this.formatDuration(session.duration);
      
      const content = [
        '{bold}{cyan-fg}Session Details{/cyan-fg}{/bold}',
        '',
        `{bold}ID:{/bold} ${session.id}`,
        `{bold}Date:{/bold} ${date.toLocaleDateString()}`,
        `{bold}Time:{/bold} ${date.toLocaleTimeString()}`,
        `{bold}Model:{/bold} ${session.model}`,
        `{bold}Provider:{/bold} ${session.provider}`,
        `{bold}Messages:{/bold} ${session.messageCount}`,
        `{bold}Duration:{/bold} ${duration}`,
        `{bold}File:{/bold} ${session.filename}`,
        '',
        '{gray-fg}Press Enter to load this session{/gray-fg}',
        '{gray-fg}Press Delete to remove this session{/gray-fg}',
      ];

      this.detailsBox.setContent(content.join('\n'));
    } else {
      this.detailsBox.setContent('Select a session to view details');
    }

    this.screen.render();
  }

  /**
   * Format duration in human readable format
   */
  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Load selected session
   */
  private loadSelected(): void {
    const selected = (this.sessionsList as any).selected;
    
    if (selected >= 0 && selected < this.filteredSessions.length) {
      const session = this.filteredSessions[selected];
      
      try {
        const filePath = join(this.sessionsDir, session.filename);
        const content = readFileSync(filePath, 'utf-8');
        const sessionData: SessionData = JSON.parse(content);
        
        if (this.options.onLoad) {
          this.options.onLoad(sessionData);
        }
        
        this.close();
      } catch (error) {
        logError('Failed to load session', error as Error);
      }
    }
  }

  /**
   * Delete selected session
   */
  private deleteSelected(): void {
    const selected = (this.sessionsList as any).selected;
    
    if (selected >= 0 && selected < this.filteredSessions.length) {
      const session = this.filteredSessions[selected];
      
      try {
        const filePath = join(this.sessionsDir, session.filename);
        unlinkSync(filePath);
        
        this.refresh();
        logDebug(`Deleted session ${session.id}`);
      } catch (error) {
        logError('Failed to delete session', error as Error);
      }
    }
  }

  /**
   * Refresh sessions list
   */
  private refresh(): void {
    this.loadSessions();
    this.filterSessions();
  }

  /**
   * Show the overlay
   */
  show(): void {
    this.container.show();
    this.sessionsList.focus();
    this.screen.render();
  }

  /**
   * Hide the overlay
   */
  hide(): void {
    this.container.hide();
    this.screen.render();
  }

  /**
   * Close the overlay
   */
  close(): void {
    this.hide();
    
    if (this.options.onClose) {
      this.options.onClose();
    }
  }

  /**
   * Destroy the overlay
   */
  destroy(): void {
    this.container.destroy();
  }
}

export default SessionsOverlay;
