/**
 * Terminal Chat Interface
 *
 * Main terminal-based chat interface using blessed.js for rich UI.
 * Handles real-time conversation, model switching, and overlays.
 */
import * as blessed from 'blessed';
import { EventEmitter } from 'events';
import { AgentLoop } from '../../utils/agent/agent-loop.js';
import { createInputItem } from '../../utils/input-utils.js';
import { logInfo, logError, logDebug, fpsDebugger } from '../../utils/logger/log.js';
import { saveRollout } from '../../utils/storage/save-rollout.js';
import { addToHistory } from '../../utils/storage/command-history.js';
import { HistoryOverlay, ModelOverlay, HelpOverlay, ApprovalModeOverlay, DiffOverlay, SessionsOverlay, } from '../overlays/index.js';
import { TerminalChatInput } from './terminal-chat-input.js';
import { TerminalChatInputThinking } from './terminal-chat-input-thinking.js';
import { nanoid } from 'nanoid';
import { showNotification } from '../../utils/notifications.js';
import { expandFileTags } from '../../utils/file-tag-utils.js';
import { buildBugReportUrl } from '../../utils/bug-report.js';
import { CLI_VERSION } from '../../version.js';
import os from 'os';
export class TerminalChat extends EventEmitter {
    config;
    screen;
    chatContainer;
    inputContainer;
    statusBar;
    agentLoop;
    // State
    sessionId;
    items = [];
    currentModel;
    currentProvider;
    approvalPolicy;
    overlayMode = 'none';
    loading = false;
    _terminalSize;
    // UI Components
    chatInput = null;
    thinkingAnimation = null;
    currentOverlay = null;
    notificationTimeout = null;
    constructor(options) {
        super();
        this.config = options.config;
        this.sessionId = nanoid();
        this.currentModel = options.config.model;
        this.currentProvider = options.config.provider;
        this.approvalPolicy = options.config.approvalMode;
        this._terminalSize = {
            columns: process.stdout.columns || 80,
            rows: process.stdout.rows || 24,
        };
        // Initialize UI
        this.initializeScreen();
        this.initializeComponents();
        this.setupEventHandlers();
        // Initialize agent loop
        this.agentLoop = new AgentLoop({
            model: this.currentModel,
            provider: this.currentProvider,
            approvalPolicy: this.approvalPolicy,
        });
        // Process initial prompt if provided
        if (options.initialPrompt) {
            this.processInitialPrompt(options.initialPrompt);
        }
        logInfo(`Terminal chat initialized with session ID: ${this.sessionId}`);
    }
    /**
     * Initialize the blessed screen
     */
    initializeScreen() {
        this.screen = blessed.screen({
            smartCSR: true,
            title: 'Kritrima AI CLI',
            cursor: {
                artificial: true,
                shape: 'line',
                blink: true,
                color: 'white',
            },
            debug: process.env.DEBUG === '1',
            dockBorders: true,
            fullUnicode: true,
        });
        // Handle screen resize
        this.screen.on('resize', () => {
            this._terminalSize = {
                columns: typeof this.screen.width === 'number' ? this.screen.width : 80,
                rows: typeof this.screen.height === 'number' ? this.screen.height : 24,
            };
            this.updateLayout();
        });
        // Enable FPS debugging if enabled
        if (process.env.DEBUG === '1') {
            try {
                fpsDebugger.start?.(this.screen);
            }
            catch (error) {
                // FPS debugger not available or failed to start
            }
        }
    }
    /**
     * Initialize UI components
     */
    initializeComponents() {
        // Chat container
        this.chatContainer = blessed.box({
            parent: this.screen,
            top: 0,
            left: 0,
            width: '100%',
            height: '100%-4',
            scrollable: true,
            alwaysScroll: true,
            scrollbar: {
                ch: ' ',
                track: {
                    bg: 'cyan',
                },
                style: {
                    inverse: true,
                },
            },
            border: {
                type: 'line',
            },
            style: {
                fg: 'white',
                border: {
                    fg: 'cyan',
                },
            },
            tags: true,
            mouse: true,
        });
        // Input container
        this.inputContainer = blessed.box({
            parent: this.screen,
            bottom: 1,
            left: 0,
            width: '100%',
            height: 3,
            border: {
                type: 'line',
            },
            style: {
                fg: 'white',
                border: {
                    fg: 'cyan',
                },
            },
        });
        // Status bar
        this.statusBar = blessed.box({
            parent: this.screen,
            bottom: 0,
            left: 0,
            width: '100%',
            height: 1,
            style: {
                fg: 'white',
                bg: 'blue',
            },
            content: this.getStatusBarContent(),
        });
        // Initialize chat input
        this.initializeChatInput();
        // Initial render
        this.screen.render();
    }
    /**
     * Initialize chat input component
     */
    initializeChatInput() {
        this.chatInput = new TerminalChatInput({
            parent: this.inputContainer,
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            workingDirectory: this.config.workingDirectory || process.cwd(),
            onSubmit: (text) => this.handleUserInput(text),
            onCancel: () => this.handleCancel(),
            onSlashCommand: (command, args) => this.handleSlashCommand(command, args),
            onHeightChange: (height) => this.updateInputHeight(height),
        });
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Global key handlers
        this.screen.key(['escape', 'q', 'C-c'], (_ch, _key) => {
            if (this.overlayMode !== 'none') {
                this.closeOverlay();
            }
            else if (this.loading) {
                this.handleCancel();
            }
            else {
                this.exit();
            }
        });
        // Navigation keys
        this.screen.key(['j', 'down'], () => {
            if (this.overlayMode === 'none') {
                this.chatContainer.scroll(1);
                this.screen.render();
            }
        });
        this.screen.key(['k', 'up'], () => {
            if (this.overlayMode === 'none') {
                this.chatContainer.scroll(-1);
                this.screen.render();
            }
        });
        // Page navigation
        this.screen.key(['pagedown', 'C-f'], () => {
            if (this.overlayMode === 'none') {
                this.chatContainer.scroll(this.chatContainer.height || 10);
                this.screen.render();
            }
        });
        this.screen.key(['pageup', 'C-b'], () => {
            if (this.overlayMode === 'none') {
                this.chatContainer.scroll(-(this.chatContainer.height || 10));
                this.screen.render();
            }
        });
        // Overlay shortcuts
        this.screen.key(['C-h'], () => this.showOverlay('history'));
        this.screen.key(['C-m'], () => this.showOverlay('model'));
        this.screen.key(['C-s'], () => this.showOverlay('sessions'));
        this.screen.key(['C-a'], () => this.showOverlay('approval'));
        this.screen.key(['C-d'], () => this.showOverlay('diff'));
        this.screen.key(['C-?', 'f1'], () => this.showOverlay('help'));
        // Focus management
        this.screen.key(['tab'], () => {
            if (this.overlayMode === 'none' && this.chatInput) {
                this.chatInput.focus();
            }
        });
    }
    /**
     * Get status bar content
     */
    getStatusBarContent() {
        const modelInfo = `${this.currentProvider}:${this.currentModel}`;
        const approvalInfo = `Approval: ${this.approvalPolicy}`;
        const sessionInfo = `Session: ${this.sessionId.slice(0, 8)}`;
        return ` ${modelInfo} | ${approvalInfo} | ${sessionInfo} | Press F1 for help `;
    }
    /**
     * Update layout after resize
     */
    updateLayout() {
        // Update status bar content
        this.statusBar.setContent(this.getStatusBarContent());
        this.screen.render();
    }
    /**
     * Update input height
     */
    updateInputHeight(height) {
        this.inputContainer.height = height;
        this.chatContainer.height = `100%-${height + 1}`;
        this.screen.render();
    }
    /**
     * Handle user input
     */
    async handleUserInput(text) {
        if (!text.trim()) {
            return;
        }
        try {
            // Add to command history
            addToHistory(text);
            // Expand file tags
            const expandedText = await expandFileTags(text);
            // Create input item
            const inputItem = await createInputItem(expandedText, []);
            // Add user message to chat
            const userMessage = {
                type: 'message',
                role: 'user',
                content: [{ type: 'input_text', text: expandedText }],
                id: nanoid(),
                timestamp: Date.now(),
            };
            this.addMessageToChat(userMessage);
            // Show thinking animation
            this.showThinkingAnimation();
            // Process through agent loop
            const response = await this.agentLoop.processInput(inputItem);
            // Hide thinking animation
            this.hideThinkingAnimation();
            // Add response to chat
            this.addResponseToChat(response);
            // Save session
            this.saveSession();
            // Show notification
            this.showResponseNotification(response);
        }
        catch (error) {
            this.hideThinkingAnimation();
            logError('Failed to process user input', error);
            this.addErrorToChat(error);
        }
    }
    /**
     * Handle cancel operation
     */
    handleCancel() {
        if (this.loading) {
            this.agentLoop.abort();
            this.hideThinkingAnimation();
            this.addSystemMessage('Operation cancelled by user');
        }
    }
    /**
     * Handle slash commands
     */
    async handleSlashCommand(command, args) {
        logDebug(`Handling slash command: ${command} with args: ${JSON.stringify(args)}`);
        switch (command) {
            case '/clear':
                this.clearChat();
                break;
            case '/compact':
                await this.compactConversation();
                break;
            case '/history':
                this.showOverlay('history');
                break;
            case '/sessions':
                this.showOverlay('sessions');
                break;
            case '/model':
                this.showOverlay('model');
                break;
            case '/approval':
                this.showOverlay('approval');
                break;
            case '/diff':
                this.showOverlay('diff');
                break;
            case '/help':
                this.showOverlay('help');
                break;
            case '/bug':
                await this.generateBugReport();
                break;
            default:
                this.addSystemMessage(`Unknown command: ${command}`);
                break;
        }
    }
    /**
     * Process initial prompt
     */
    async processInitialPrompt(prompt) {
        // Wait for UI to be ready
        await new Promise(resolve => process.nextTick(resolve));
        // Set the input text and submit
        if (this.chatInput) {
            this.chatInput.setText(prompt);
            await this.handleUserInput(prompt);
        }
    }
    /**
     * Show thinking animation
     */
    showThinkingAnimation() {
        this.loading = true;
        if (this.chatInput) {
            this.chatInput.hide();
        }
        this.thinkingAnimation = new TerminalChatInputThinking({
            parent: this.inputContainer,
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            messages: [
                'Thinking...',
                'Processing your request...',
                'Analyzing context...',
                'Generating response...',
                'Almost ready...',
            ],
            showProgress: true,
        });
        this.thinkingAnimation.start();
        this.screen.render();
    }
    /**
     * Hide thinking animation
     */
    hideThinkingAnimation() {
        this.loading = false;
        if (this.thinkingAnimation) {
            this.thinkingAnimation.stop();
            this.thinkingAnimation.destroy();
            this.thinkingAnimation = null;
        }
        if (this.chatInput) {
            this.chatInput.show();
            this.chatInput.focus();
        }
        this.screen.render();
    }
    /**
     * Add message to chat
     */
    addMessageToChat(item) {
        this.items.push(item);
        this.updateChatDisplay();
    }
    /**
     * Add response to chat
     */
    addResponseToChat(response) {
        this.items.push(response);
        this.updateChatDisplay();
    }
    /**
     * Add error to chat
     */
    addErrorToChat(error) {
        this.addSystemMessage(`Error: ${error.message}`, 'error');
    }
    /**
     * Add system message to chat
     */
    addSystemMessage(message, _messageType = 'info') {
        const item = {
            type: 'message',
            role: 'system',
            content: [{ type: 'input_text', text: message }],
            id: nanoid(),
            timestamp: Date.now(),
        };
        this.addMessageToChat(item);
    }
    /**
     * Update chat display
     */
    updateChatDisplay() {
        const content = this.items.map((item, index) => {
            return this.renderChatItem(item, index);
        }).join('\n\n');
        this.chatContainer.setContent(content);
        this.chatContainer.scrollTo(this.chatContainer.getScrollHeight());
        this.screen.render();
    }
    /**
     * Render chat item
     */
    renderChatItem(item, _index) {
        const timestamp = new Date().toLocaleTimeString();
        // Handle different item types
        if ('role' in item) {
            // ResponseInputItem or ResponseOutputItem
            switch (item.role) {
                case 'user':
                    return `{cyan-fg}[${timestamp}] You:{/cyan-fg}\n${this.renderContent(item.content)}`;
                case 'assistant':
                    return `{green-fg}[${timestamp}] AI:{/green-fg}\n${this.renderContent(item.content)}`;
                case 'system':
                    return `{yellow-fg}[${timestamp}] System:{/yellow-fg}\n${this.renderContent(item.content)}`;
                case 'tool':
                    return `{magenta-fg}[${timestamp}] Tool:{/magenta-fg}\n${this.renderContent(item.content)}`;
                default:
                    return `{gray-fg}[${timestamp}] Unknown:{/gray-fg}\n${this.renderContent(item.content || [])}`;
            }
        }
        else {
            // ResponseItem
            switch (item.type) {
                case 'message':
                    return `{white-fg}[${timestamp}] Message:{/white-fg}\n${this.renderContent(item.content)}`;
                case 'function_call':
                    return `{blue-fg}[${timestamp}] Function Call:{/blue-fg}\n${this.renderContent(item.content)}`;
                case 'function_result':
                    return `{green-fg}[${timestamp}] Function Result:{/green-fg}\n${this.renderContent(item.content)}`;
                case 'error':
                    return `{red-fg}[${timestamp}] Error:{/red-fg}\n${this.renderContent(item.content)}`;
                case 'system':
                    return `{yellow-fg}[${timestamp}] System:{/yellow-fg}\n${this.renderContent(item.content)}`;
                default:
                    return `{gray-fg}[${timestamp}] Unknown:{/gray-fg}\n${this.renderContent(item.content || [])}`;
            }
        }
    }
    /**
     * Render content array
     */
    renderContent(content) {
        return content.map(item => {
            switch (item.type) {
                case 'input_text':
                    return item.text;
                case 'input_image':
                    return '[Image]';
                case 'output_text':
                    return item.text;
                default:
                    return JSON.stringify(item);
            }
        }).join('');
    }
    /**
     * Show overlay
     */
    showOverlay(type) {
        if (this.overlayMode !== 'none') {
            this.closeOverlay();
        }
        this.overlayMode = type;
        switch (type) {
            case 'history':
                this.currentOverlay = new HistoryOverlay({
                    parent: this.screen,
                    onClose: () => this.closeOverlay(),
                    onSelect: (command) => this.handleHistorySelect(command),
                });
                break;
            case 'model':
                this.currentOverlay = new ModelOverlay({
                    parent: this.screen,
                    currentModel: this.currentModel,
                    currentProvider: this.currentProvider,
                    onClose: () => this.closeOverlay(),
                    onSelect: (provider, model) => this.handleModelSelect(provider, model),
                });
                break;
            case 'sessions':
                this.currentOverlay = new SessionsOverlay({
                    parent: this.screen,
                    onClose: () => this.closeOverlay(),
                });
                break;
            case 'approval':
                this.currentOverlay = new ApprovalModeOverlay({
                    parent: this.screen,
                    currentMode: this.approvalPolicy,
                    onClose: () => this.closeOverlay(),
                    onSelect: (mode) => this.handleApprovalModeSelect(mode),
                });
                break;
            case 'diff':
                this.showDiffOverlay();
                break;
            case 'help':
                this.currentOverlay = new HelpOverlay({
                    parent: this.screen,
                    onClose: () => this.closeOverlay(),
                });
                break;
        }
        this.screen.render();
    }
    /**
     * Close current overlay
     */
    closeOverlay() {
        if (this.currentOverlay) {
            this.currentOverlay.destroy();
            this.currentOverlay = null;
        }
        this.overlayMode = 'none';
        if (this.chatInput) {
            this.chatInput.focus();
        }
        this.screen.render();
    }
    /**
     * Handle history selection
     */
    handleHistorySelect(command) {
        this.closeOverlay();
        if (this.chatInput) {
            this.chatInput.setText(command);
            this.chatInput.focus();
        }
    }
    /**
     * Handle model selection
     */
    handleModelSelect(provider, model) {
        this.currentProvider = provider;
        this.currentModel = model;
        // Update agent loop
        this.agentLoop.updateConfig({
            provider,
            model,
            approvalPolicy: this.approvalPolicy,
        });
        // Update status bar
        this.statusBar.setContent(this.getStatusBarContent());
        this.closeOverlay();
        this.addSystemMessage(`Switched to ${provider}:${model}`);
    }
    /**
     * Handle session selection
     */
    _handleSessionSelect(sessionId) {
        this.closeOverlay();
        // TODO: Implement session loading
        this.addSystemMessage(`Loading session ${sessionId}...`);
    }
    /**
     * Handle approval mode selection
     */
    handleApprovalModeSelect(mode) {
        this.approvalPolicy = mode;
        // Update agent loop
        this.agentLoop.updateConfig({
            provider: this.currentProvider,
            model: this.currentModel,
            approvalPolicy: mode,
        });
        // Update status bar
        this.statusBar.setContent(this.getStatusBarContent());
        this.closeOverlay();
        this.addSystemMessage(`Approval mode changed to: ${mode}`);
    }
    /**
     * Show diff overlay
     */
    async showDiffOverlay() {
        try {
            this.currentOverlay = new DiffOverlay({
                parent: this.screen,
                workingDirectory: this.config.workingDirectory,
                onClose: () => this.closeOverlay(),
            });
            this.screen.render();
        }
        catch (error) {
            this.addSystemMessage(`Failed to get git diff: ${error.message}`, 'error');
        }
    }
    /**
     * Clear chat
     */
    clearChat() {
        this.items = [];
        this.updateChatDisplay();
        this.addSystemMessage('Chat cleared');
    }
    /**
     * Compact conversation
     */
    async compactConversation() {
        // TODO: Implement conversation compaction
        this.addSystemMessage('Conversation compaction not yet implemented');
    }
    /**
     * Generate bug report
     */
    async generateBugReport() {
        try {
            const url = buildBugReportUrl({
                items: this.items,
                cliVersion: CLI_VERSION,
                model: this.currentModel,
                provider: this.currentProvider,
                nodeVersion: process.version,
                platform: [os.platform(), os.arch(), os.release()].join(' '),
            });
            this.addSystemMessage(`Bug report URL generated: ${url}`);
        }
        catch (error) {
            this.addSystemMessage(`Failed to generate bug report: ${error.message}`, 'error');
        }
    }
    /**
     * Save session
     */
    saveSession() {
        try {
            saveRollout(this.sessionId, this.items);
        }
        catch (error) {
            logError('Failed to save session', error);
        }
    }
    /**
     * Show response notification
     */
    showResponseNotification(response) {
        if (this.config.notifications !== false) {
            const preview = this.getResponsePreview(response);
            showNotification('AI Response', preview, this.config.workingDirectory);
        }
    }
    /**
     * Get response preview for notifications
     */
    getResponsePreview(response) {
        if (response.content && Array.isArray(response.content)) {
            const textContent = response.content
                .filter((item) => item.type === 'output_text')
                .map((item) => item.text)
                .join(' ');
            return textContent.slice(0, 100) + (textContent.length > 100 ? '...' : '');
        }
        return 'AI response received';
    }
    /**
     * Start the chat interface
     */
    start() {
        if (this.chatInput) {
            this.chatInput.focus();
        }
        this.screen.render();
        logInfo('Terminal chat interface started');
    }
    /**
     * Exit the application
     */
    exit() {
        this.saveSession();
        process.exit(0);
    }
    /**
     * Destroy the chat interface
     */
    destroy() {
        if (this.currentOverlay) {
            this.currentOverlay.destroy();
        }
        if (this.thinkingAnimation) {
            this.thinkingAnimation.destroy();
        }
        if (this.chatInput) {
            this.chatInput.destroy();
        }
        if (this.notificationTimeout) {
            clearTimeout(this.notificationTimeout);
        }
        this.screen.destroy();
        this.removeAllListeners();
    }
}
//# sourceMappingURL=terminal-chat.js.map