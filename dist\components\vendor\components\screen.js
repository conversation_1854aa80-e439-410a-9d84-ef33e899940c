/**
 * Enhanced Screen Component
 *
 * Wrapper around blessed.screen with additional functionality
 * for better integration and management.
 */
import * as blessed from 'blessed';
import { EventEmitter } from 'events';
import { logDebug, logError } from '../../../utils/logger/log.js';
import { toNumber } from '../../../types/blessed-extensions.js';
export class EnhancedScreen extends EventEmitter {
    screen;
    options;
    isDestroyed = false;
    constructor(options = {}) {
        super();
        this.options = options;
        // Create blessed screen with enhanced defaults
        this.screen = blessed.screen({
            smartCSR: true,
            title: 'Kritrima AI CLI',
            cursor: {
                artificial: true,
                shape: 'line',
                blink: true,
                color: 'white',
            },
            debug: process.env.DEBUG === '1',
            dockBorders: true,
            fullUnicode: true,
            autoPadding: true,
            warnings: false,
            ...options,
        });
        this.setupEventHandlers();
        this.setupGlobalKeyHandlers();
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Handle resize events
        this.screen.on('resize', () => {
            const width = toNumber(this.screen.width);
            const height = toNumber(this.screen.height);
            logDebug(`Screen resized: ${width}x${height}`);
            if (this.options.onResize) {
                this.options.onResize(width, height);
            }
            this.emit('resize', width, height);
        });
        // Handle keypress events
        this.screen.on('keypress', (ch, key) => {
            if (this.options.onKeypress) {
                this.options.onKeypress(ch, key);
            }
            this.emit('keypress', ch, key);
        });
        // Handle warnings
        this.screen.on('warning', (warning) => {
            logError('Screen warning:', new Error(warning));
        });
        // Handle element events
        this.screen.on('element click', (el, data) => {
            this.emit('elementClick', el, data);
        });
        this.screen.on('element focus', (el) => {
            this.emit('elementFocus', el);
        });
        this.screen.on('element blur', (el) => {
            this.emit('elementBlur', el);
        });
    }
    /**
     * Setup global key handlers
     */
    setupGlobalKeyHandlers() {
        // Global exit handlers
        this.screen.key(['C-c'], () => {
            if (this.options.onExit) {
                this.options.onExit();
            }
            else {
                this.exit();
            }
        });
        // Debug key handler
        if (process.env.DEBUG === '1') {
            this.screen.key(['C-d'], () => {
                this.toggleDebug();
            });
        }
    }
    /**
     * Get the underlying blessed screen
     */
    getScreen() {
        return this.screen;
    }
    /**
     * Render the screen
     */
    render() {
        if (!this.isDestroyed) {
            this.screen.render();
        }
    }
    /**
     * Clear the screen
     */
    clear() {
        this.screen.clearRegion(0, toNumber(this.screen.width), 0, toNumber(this.screen.height));
        this.render();
    }
    /**
     * Focus an element
     */
    focus(element) {
        element.focus?.();
        this.render();
    }
    /**
     * Get screen dimensions
     */
    getDimensions() {
        return {
            width: toNumber(this.screen.width),
            height: toNumber(this.screen.height),
        };
    }
    /**
     * Set screen title
     */
    setTitle(title) {
        this.screen.title = title;
    }
    /**
     * Toggle debug mode
     */
    toggleDebug() {
        const debug = !this.screen.options.debug;
        this.screen.options.debug = debug;
        logDebug(`Screen debug mode: ${debug ? 'enabled' : 'disabled'}`);
    }
    /**
     * Add global key handler
     */
    key(keys, callback) {
        this.screen.key(keys, callback);
    }
    /**
     * Remove key handler
     */
    unkey(keys, callback) {
        if (Array.isArray(keys)) {
            keys.forEach(key => this.screen.unkey(key, callback));
        }
        else {
            this.screen.unkey(keys, callback);
        }
    }
    /**
     * Save screen state
     */
    saveState() {
        this.screen.saveCursor?.();
    }
    /**
     * Restore screen state
     */
    restoreState() {
        this.screen.restoreCursor?.();
    }
    /**
     * Take screenshot (debug feature)
     */
    screenshot() {
        return this.screen.screenshot?.() || '';
    }
    /**
     * Exit the application
     */
    exit(code = 0) {
        this.destroy();
        process.exit(code);
    }
    /**
     * Destroy the screen
     */
    destroy() {
        if (this.isDestroyed) {
            return;
        }
        this.isDestroyed = true;
        try {
            this.screen.destroy();
            this.removeAllListeners();
        }
        catch (error) {
            logError('Error destroying screen:', error);
        }
    }
    /**
     * Check if screen is destroyed
     */
    get destroyed() {
        return this.isDestroyed;
    }
    /**
     * Get screen width
     */
    get width() {
        return toNumber(this.screen.width);
    }
    /**
     * Get screen height
     */
    get height() {
        return toNumber(this.screen.height);
    }
    /**
     * Get screen title
     */
    get title() {
        return this.screen.title;
    }
    /**
     * Proxy method calls to underlying screen
     */
    append(element) {
        this.screen.append(element);
    }
    remove(element) {
        this.screen.remove(element);
    }
    insert(element, index) {
        this.screen.insert(element, index);
    }
    insertBefore(element, refElement) {
        this.screen.insertBefore(element, refElement);
    }
    insertAfter(element, refElement) {
        this.screen.insertAfter(element, refElement);
    }
}
export default EnhancedScreen;
//# sourceMappingURL=screen.js.map