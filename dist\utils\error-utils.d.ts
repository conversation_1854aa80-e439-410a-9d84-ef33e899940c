/**
 * Error handling utilities
 *
 * Provides safe error handling for unknown error types
 */
/**
 * Safely convert unknown error to Error object
 */
export declare function toError(error: unknown): Error;
/**
 * Safely get error message from unknown error
 */
export declare function getErrorMessage(error: unknown): string;
/**
 * Safely get error status code from unknown error
 */
export declare function getErrorStatus(error: unknown): number | undefined;
/**
 * Check if error has a specific property
 */
export declare function hasErrorProperty(error: unknown, property: string): boolean;
/**
 * Safely get error property value
 */
export declare function getErrorProperty(error: unknown, property: string): unknown;
/**
 * Create a standardized error object with additional context
 */
export declare function createError(message: string, originalError?: unknown, context?: Record<string, unknown>): Error;
/**
 * Wrap async function with error handling
 */
export declare function withErrorHandling<T extends any[], R>(fn: (...args: T) => Promise<R>, errorHandler?: (error: unknown) => void): (...args: T) => Promise<R | undefined>;
/**
 * Wrap sync function with error handling
 */
export declare function withSyncErrorHandling<T extends any[], R>(fn: (...args: T) => R, errorHandler?: (error: unknown) => void): (...args: T) => R | undefined;
//# sourceMappingURL=error-utils.d.ts.map