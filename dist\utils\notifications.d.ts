/**
 * Desktop Notifications System
 *
 * Cross-platform desktop notifications for important events,
 * command completions, and user alerts with smart throttling.
 */
export interface NotificationOptions {
    title: string;
    message: string;
    type?: 'info' | 'success' | 'warning' | 'error';
    timeout?: number;
    sound?: boolean;
    icon?: string;
    actions?: string[];
    onClick?: () => void;
    onTimeout?: () => void;
    workingDirectory?: string;
}
export interface NotificationConfig {
    enabled: boolean;
    soundEnabled: boolean;
    timeout: number;
    throttleMs: number;
    maxPerMinute: number;
}
/**
 * Notification manager with throttling and configuration
 */
export declare class NotificationManager {
    private config;
    private recentNotifications;
    private notificationCount;
    private lastMinuteReset;
    constructor(config?: Partial<NotificationConfig>);
    /**
     * Send a notification with throttling
     */
    notify(options: NotificationOptions): Promise<void>;
    /**
     * Send success notification
     */
    success(title: string, message: string, options?: Partial<NotificationOptions>): Promise<void>;
    /**
     * Send error notification
     */
    error(title: string, message: string, options?: Partial<NotificationOptions>): Promise<void>;
    /**
     * Send warning notification
     */
    warning(title: string, message: string, options?: Partial<NotificationOptions>): Promise<void>;
    /**
     * Send info notification
     */
    info(title: string, message: string, options?: Partial<NotificationOptions>): Promise<void>;
    /**
     * Send command completion notification
     */
    commandComplete(command: string, success: boolean, duration: number): Promise<void>;
    /**
     * Send agent action notification
     */
    agentAction(action: string, details?: string): Promise<void>;
    /**
     * Build notification options for node-notifier
     */
    private buildNotificationOptions;
    /**
     * Check rate limiting
     */
    private checkRateLimit;
    /**
     * Track notification for throttling
     */
    private trackNotification;
    /**
     * Get platform-appropriate icons
     */
    private getSuccessIcon;
    private getErrorIcon;
    private getWarningIcon;
    private getInfoIcon;
    /**
     * Format duration for display
     */
    private formatDuration;
    /**
     * Update configuration
     */
    updateConfig(config: Partial<NotificationConfig>): void;
    /**
     * Enable/disable notifications
     */
    setEnabled(enabled: boolean): void;
    /**
     * Enable/disable sounds
     */
    setSoundEnabled(enabled: boolean): void;
    /**
     * Clear notification history (for testing)
     */
    clearHistory(): void;
}
export declare const notifications: NotificationManager;
export declare const notifySuccess: (title: string, message: string, options?: Partial<NotificationOptions>) => Promise<void>;
export declare const notifyError: (title: string, message: string, options?: Partial<NotificationOptions>) => Promise<void>;
export declare const notifyWarning: (title: string, message: string, options?: Partial<NotificationOptions>) => Promise<void>;
export declare const notifyInfo: (title: string, message: string, options?: Partial<NotificationOptions>) => Promise<void>;
export declare const notifyCommandComplete: (command: string, success: boolean, duration: number) => Promise<void>;
export declare const notifyAgentAction: (action: string, details?: string) => Promise<void>;
/**
 * Simple notification function for backward compatibility
 */
export declare const showNotification: (title: string, message: string, workingDirectory?: string) => Promise<void>;
export default notifications;
//# sourceMappingURL=notifications.d.ts.map