/**
 * Command History Management
 *
 * Persistent command history with sensitive data filtering,
 * search capabilities, and configurable size limits.
 */
import type { HistoryEntry, HistoryConfig } from '../../types/index.js';
/**
 * Add command to history
 */
export declare function addToHistory(command: string, sessionId?: string, success?: boolean): void;
/**
 * Get command history
 */
export declare function getHistory(): HistoryEntry[];
/**
 * Search command history
 */
export declare function searchHistory(query: string, limit?: number): HistoryEntry[];
/**
 * Get recent commands
 */
export declare function getRecentCommands(limit?: number): HistoryEntry[];
/**
 * Get commands by session
 */
export declare function getCommandsBySession(sessionId: string): HistoryEntry[];
/**
 * Get command statistics
 */
export declare function getHistoryStats(): {
    totalCommands: number;
    uniqueCommands: number;
    successfulCommands: number;
    failedCommands: number;
    averageCommandLength: number;
    mostUsedCommands: Array<{
        command: string;
        count: number;
    }>;
    oldestCommand?: Date;
    newestCommand?: Date;
};
/**
 * Clear command history
 */
export declare function clearHistory(): void;
/**
 * Remove command from history
 */
export declare function removeFromHistory(index: number): boolean;
/**
 * Update history configuration
 */
export declare function updateHistoryConfig(newConfig: Partial<HistoryConfig>): void;
/**
 * Export history to file
 */
export declare function exportHistory(outputPath: string): boolean;
/**
 * Import history from file
 */
export declare function importHistory(inputPath: string, merge?: boolean): boolean;
/**
 * Get command suggestions based on partial input
 */
export declare function getCommandSuggestions(partial: string, limit?: number): string[];
//# sourceMappingURL=command-history.d.ts.map