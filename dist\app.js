/**
 * Main Application Layer
 *
 * Handles application initialization, Git validation, safety checks,
 * and UI orchestration for the Kritrima AI CLI.
 */
import { existsSync } from 'fs';
import chalk from 'chalk';
import { checkInGit } from './utils/check-in-git';
import { getApi<PERSON><PERSON> } from './utils/config';
import { createOpenAIClient, testClientConnection } from './utils/openai-client';
import { logInfo, logError, logWarn } from './utils/logger/log';
import { TerminalChat } from './components/chat/terminal-chat';
/**
 * Run the main application
 */
export async function runApp(config, initialPrompt) {
    try {
        // Perform pre-flight checks
        await performPreflightChecks(config);
        // Initialize terminal chat interface
        const terminalChat = new TerminalChat({
            config,
            initialPrompt,
        });
        // Start the chat interface
        terminalChat.start();
    }
    catch (error) {
        logError('Application startup failed', error);
        throw error;
    }
}
/**
 * Perform pre-flight checks before starting the application
 */
async function performPreflightChecks(config) {
    logInfo('Performing pre-flight checks...');
    // Check working directory
    await checkWorkingDirectory(config);
    // Check Git repository status
    await checkGitRepository(config);
    // Validate API configuration
    await validateApiConfiguration(config);
    // Test API connection
    await testApiConnection(config);
    logInfo('Pre-flight checks completed successfully');
}
/**
 * Check working directory exists and is accessible
 */
async function checkWorkingDirectory(config) {
    const workdir = config.workingDirectory || process.cwd();
    if (!existsSync(workdir)) {
        throw new Error(`Working directory does not exist: ${workdir}`);
    }
    try {
        // Test write access by attempting to change to the directory
        process.chdir(workdir);
        logInfo(`Working directory: ${workdir}`);
    }
    catch (error) {
        throw new Error(`Cannot access working directory: ${workdir}`);
    }
}
/**
 * Check Git repository status and warn if not in a Git repo
 */
async function checkGitRepository(config) {
    const workdir = config.workingDirectory || process.cwd();
    if (!checkInGit(workdir)) {
        logWarn('Not in a Git repository - some features may be limited');
        // Show warning to user
        console.log(chalk.yellow('⚠️  Warning: You are not in a Git repository.'));
        console.log(chalk.yellow('   Some features like diff generation and change tracking will be limited.'));
        console.log(chalk.yellow('   Consider initializing a Git repository with: git init'));
        console.log('');
    }
    else {
        logInfo('Git repository detected');
    }
}
/**
 * Validate API configuration
 */
async function validateApiConfiguration(config) {
    const apiKey = getApiKey(config.provider);
    if (!apiKey) {
        const envKey = getProviderEnvKey(config.provider);
        throw new Error(`API key not found for provider: ${config.provider}\n` +
            `Please set the ${envKey} environment variable.`);
    }
    logInfo(`API key found for provider: ${config.provider}`);
}
/**
 * Test API connection
 */
async function testApiConnection(config) {
    try {
        logInfo('Testing API connection...');
        const client = createOpenAIClient({
            provider: config.provider,
            timeout: 10000, // Short timeout for connection test
        });
        const isConnected = await testClientConnection(client, config.model);
        if (!isConnected) {
            throw new Error('API connection test failed');
        }
        logInfo('API connection test successful');
    }
    catch (error) {
        const err = error;
        logError('API connection test failed', err);
        // Provide helpful error messages
        if (err.message.includes('401') || err.message.includes('authentication')) {
            throw new Error(`Authentication failed for provider: ${config.provider}\n` +
                'Please check your API key is correct and has the necessary permissions.');
        }
        else if (err.message.includes('404') || err.message.includes('not found')) {
            throw new Error(`Model not found: ${config.model}\n` +
                `Please check that the model is available for provider: ${config.provider}`);
        }
        else if (err.message.includes('timeout')) {
            throw new Error(`Connection timeout to provider: ${config.provider}\n` +
                'Please check your internet connection and try again.');
        }
        else {
            throw new Error(`Failed to connect to provider: ${config.provider}\n` +
                `Error: ${err.message}`);
        }
    }
}
/**
 * Get environment key for provider
 */
function getProviderEnvKey(provider) {
    const envKeys = {
        openai: 'OPENAI_API_KEY',
        azure: 'AZURE_OPENAI_API_KEY',
        gemini: 'GEMINI_API_KEY',
        ollama: 'OLLAMA_API_KEY',
        mistral: 'MISTRAL_API_KEY',
        deepseek: 'DEEPSEEK_API_KEY',
        xai: 'XAI_API_KEY',
        groq: 'GROQ_API_KEY',
        arceeai: 'ARCEEAI_API_KEY',
        openrouter: 'OPENROUTER_API_KEY',
    };
    return envKeys[provider.toLowerCase()] || `${provider.toUpperCase()}_API_KEY`;
}
/**
 * Handle graceful shutdown
 */
export function setupGracefulShutdown() {
    const shutdown = (signal) => {
        logInfo(`Received ${signal}, shutting down gracefully...`);
        // Perform cleanup
        try {
            // Clear terminal state
            process.stdout.write('\x1b[?25h'); // Show cursor
            process.stdout.write('\x1b[0m'); // Reset colors
            logInfo('Shutdown complete');
            process.exit(0);
        }
        catch (error) {
            logError('Error during shutdown', error);
            process.exit(1);
        }
    };
    // Handle various shutdown signals
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGQUIT', () => shutdown('SIGQUIT'));
    // Handle Windows signals
    if (process.platform === 'win32') {
        process.on('SIGBREAK', () => shutdown('SIGBREAK'));
    }
}
/**
 * Display startup banner
 */
export function displayStartupBanner(config) {
    console.log(chalk.cyan('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.cyan('║                    Kritrima AI CLI                          ║'));
    console.log(chalk.cyan('║              Autonomous Coding Assistant                    ║'));
    console.log(chalk.cyan('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
    console.log(chalk.blue(`Provider: ${config.provider}`));
    console.log(chalk.blue(`Model: ${config.model}`));
    console.log(chalk.blue(`Approval Mode: ${config.approvalMode}`));
    console.log('');
    console.log(chalk.green('Type your message or use slash commands:'));
    console.log(chalk.gray('  /help    - Show help'));
    console.log(chalk.gray('  /model   - Switch model'));
    console.log(chalk.gray('  /history - View history'));
    console.log(chalk.gray('  /clear   - Clear conversation'));
    console.log('');
}
/**
 * Handle application errors
 */
export function handleApplicationError(error) {
    logError('Application error', error);
    // Display user-friendly error message
    console.error(chalk.red('\n❌ Application Error:'));
    console.error(chalk.red(error.message));
    // Provide recovery suggestions
    if (error.message.includes('API key')) {
        console.error(chalk.yellow('\n💡 Suggestion:'));
        console.error(chalk.yellow('   Check your API key configuration with: kritrima-ai doctor'));
    }
    else if (error.message.includes('connection') || error.message.includes('network')) {
        console.error(chalk.yellow('\n💡 Suggestion:'));
        console.error(chalk.yellow('   Check your internet connection and try again'));
    }
    else if (error.message.includes('permission') || error.message.includes('access')) {
        console.error(chalk.yellow('\n💡 Suggestion:'));
        console.error(chalk.yellow('   Check file permissions and working directory access'));
    }
    console.error(chalk.gray('\nFor more help, run: kritrima-ai doctor'));
}
/**
 * Initialize application with error handling
 */
export async function initializeApp(config, initialPrompt) {
    try {
        // Setup graceful shutdown
        setupGracefulShutdown();
        // Display startup banner
        displayStartupBanner(config);
        // Run the main application
        await runApp(config, initialPrompt);
    }
    catch (error) {
        handleApplicationError(error);
        process.exit(1);
    }
}
//# sourceMappingURL=app.js.map