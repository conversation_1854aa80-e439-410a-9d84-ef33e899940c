/**
 * Terminal Chat Response Item Component
 *
 * Renders individual chat messages with syntax highlighting,
 * code blocks, streaming support, and interactive elements.
 */
import * as blessed from 'blessed';
import type { ResponseItem, ResponseOutputItem } from '../../types/index.js';
export interface ResponseItemOptions {
    parent: blessed.Widgets.Node;
    item: ResponseItem | ResponseOutputItem;
    width?: string | number;
    onCommandClick?: (command: string[]) => void;
    onFileClick?: (filePath: string) => void;
    showTimestamp?: boolean;
    compact?: boolean;
}
export declare class TerminalChatResponseItem {
    private container;
    private options;
    private item;
    constructor(options: ResponseItemOptions);
    /**
     * Render the response item
     */
    private render;
    /**
     * Build content based on item type
     */
    private buildContent;
    /**
     * Render message content
     */
    private renderMessage;
    /**
     * Render input text (user message)
     */
    private renderInputText;
    /**
     * Render output text (AI response)
     */
    private renderOutputText;
    /**
     * Render tool use
     */
    private renderToolUse;
    /**
     * Render tool result
     */
    private renderToolResult;
    /**
     * Render function call
     */
    private renderFunctionCall;
    /**
     * Render error
     */
    private renderError;
    /**
     * Render command output
     */
    private renderCommandOutput;
    /**
     * Process text content for special formatting
     */
    private processTextContent;
    /**
     * Process markdown-like formatting
     */
    private processMarkdown;
    /**
     * Wrap text to fit width with indentation
     */
    private wrapText;
    /**
     * Update the item content
     */
    updateItem(item: ResponseItem | ResponseOutputItem): void;
    /**
     * Get the container element
     */
    getElement(): blessed.Widgets.BoxElement;
    /**
     * Get the item height
     */
    getHeight(): number;
    /**
     * Scroll to make this item visible
     */
    scrollIntoView(): void;
    /**
     * Destroy the component
     */
    destroy(): void;
}
/**
 * Create a response item component
 */
export declare function createResponseItem(parent: blessed.Widgets.Node, item: ResponseItem | ResponseOutputItem, options?: Partial<ResponseItemOptions>): TerminalChatResponseItem;
export default TerminalChatResponseItem;
//# sourceMappingURL=terminal-chat-response-item.d.ts.map