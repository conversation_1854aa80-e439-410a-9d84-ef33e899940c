/**
 * Platform Command Adaptation
 *
 * Converts Unix commands to Windows equivalents and handles
 * cross-platform command compatibility.
 */
/**
 * Adapt command for current platform
 */
export declare function adaptCommandForPlatform(command: string[]): string[];
/**
 * Check if command is available on current platform
 */
export declare function isCommandAvailable(command: string): boolean;
/**
 * Get platform-specific command equivalent
 */
export declare function getPlatformCommand(unixCommand: string): string;
/**
 * Validate command for current platform
 */
export declare function validatePlatformCommand(command: string[]): {
    valid: boolean;
    adapted: string[];
    warnings: string[];
};
/**
 * Get shell for current platform
 */
export declare function getPlatformShell(): string;
/**
 * Get shell flags for command execution
 */
export declare function getShellFlags(): string[];
/**
 * Execute command with platform-appropriate shell
 */
export declare function wrapCommandForShell(command: string[]): string[];
//# sourceMappingURL=platform-commands.d.ts.map