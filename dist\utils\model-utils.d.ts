/**
 * Model Management System
 *
 * Handles dynamic model discovery, validation, and management
 * across different AI providers with caching and optimization.
 */
/**
 * Fetch available models from a provider with caching
 */
export declare function fetchModels(provider: string): Promise<string[]>;
/**
 * Get models with background refresh
 */
export declare function getModelsWithRefresh(provider: string): Promise<string[]>;
/**
 * Validate if a model is available for a provider
 */
export declare function validateModel(provider: string, model: string): Promise<boolean>;
/**
 * Get model information including context length and capabilities
 */
export declare function getModelInfo(provider: string, model: string): {
    contextLength: number;
    supportsImages: boolean;
    supportsTools: boolean;
    isChat: boolean;
    isCompletion: boolean;
};
/**
 * Calculate token count estimate for text
 */
export declare function estimateTokenCount(text: string): number;
/**
 * Check if context fits within model limits
 */
export declare function checkContextLimit(provider: string, model: string, messages: Array<{
    content: string;
}>): {
    fits: boolean;
    estimatedTokens: number;
    maxTokens: number;
    usage: number;
};
/**
 * Get recommended models for a provider
 */
export declare function getRecommendedModels(provider: string): string[];
/**
 * Clear model cache
 */
export declare function clearModelCache(): void;
/**
 * Get cache statistics
 */
export declare function getCacheStats(): {
    providers: number;
    totalModels: number;
    oldestCache: number;
    newestCache: number;
};
//# sourceMappingURL=model-utils.d.ts.map