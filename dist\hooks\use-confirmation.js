/**
 * Confirmation Hook
 *
 * Manages confirmation queue and provides promise-based confirmation API
 * for command approval and user interactions.
 */
import { EventEmitter } from 'events';
import blessed from 'blessed';
class ConfirmationManager extends EventEmitter {
    queue = [];
    currentRequest = null;
    isProcessing = false;
    /**
     * Request confirmation from user
     */
    async requestConfirmation(prompt, explanation) {
        return new Promise((resolve, reject) => {
            const request = {
                id: Math.random().toString(36).substr(2, 9),
                prompt,
                explanation,
                resolve,
                reject,
            };
            this.queue.push(request);
            this.processQueue();
        });
    }
    /**
     * Submit confirmation result
     */
    submitConfirmation(result) {
        if (!this.currentRequest) {
            return;
        }
        const request = this.currentRequest;
        this.currentRequest = null;
        this.isProcessing = false;
        request.resolve(result);
        this.processQueue();
    }
    /**
     * Cancel current confirmation
     */
    cancelConfirmation(reason) {
        if (!this.currentRequest) {
            return;
        }
        const request = this.currentRequest;
        this.currentRequest = null;
        this.isProcessing = false;
        request.reject(new Error(reason || 'Confirmation cancelled'));
        this.processQueue();
    }
    /**
     * Get current confirmation request
     */
    getCurrentRequest() {
        return this.currentRequest;
    }
    /**
     * Check if confirmation is pending
     */
    isPending() {
        return this.isProcessing || this.queue.length > 0;
    }
    /**
     * Clear all pending confirmations
     */
    clearQueue() {
        // Reject all pending requests
        for (const request of this.queue) {
            request.reject(new Error('Confirmation queue cleared'));
        }
        if (this.currentRequest) {
            this.currentRequest.reject(new Error('Confirmation cancelled'));
            this.currentRequest = null;
        }
        this.queue = [];
        this.isProcessing = false;
    }
    /**
     * Process confirmation queue
     */
    processQueue() {
        if (this.isProcessing || this.queue.length === 0) {
            return;
        }
        this.isProcessing = true;
        this.currentRequest = this.queue.shift();
        this.emit('confirmation-requested', this.currentRequest);
    }
}
// Global confirmation manager instance
const confirmationManager = new ConfirmationManager();
/**
 * Use confirmation hook
 */
export function useConfirmation() {
    const currentRequest = confirmationManager.getCurrentRequest();
    return {
        submitConfirmation: (result) => {
            confirmationManager.submitConfirmation(result);
        },
        requestConfirmation: (prompt, explanation) => {
            return confirmationManager.requestConfirmation(prompt, explanation);
        },
        confirmationPrompt: currentRequest?.prompt || null,
        explanation: currentRequest?.explanation,
        isPending: confirmationManager.isPending(),
    };
}
/**
 * Create confirmation prompt element
 */
export function createConfirmationPrompt(screen, message, options = {}) {
    const prompt = blessed.box({
        parent: screen,
        top: 'center',
        left: 'center',
        width: options.width || '60%',
        height: options.height || 'shrink',
        content: message,
        tags: true,
        border: options.border !== false ? {
            type: 'line',
        } : undefined,
        style: {
            fg: 'white',
            bg: 'blue',
            border: {
                fg: 'cyan',
            },
        },
        padding: {
            top: 1,
            bottom: 1,
            left: 2,
            right: 2,
        },
    });
    if (options.title) {
        prompt.setLabel(options.title);
    }
    return prompt;
}
/**
 * Create yes/no confirmation dialog
 */
export function createYesNoDialog(screen, message, title) {
    return new Promise((resolve) => {
        const dialog = blessed.question({
            parent: screen,
            top: 'center',
            left: 'center',
            width: '50%',
            height: 'shrink',
            content: message,
            tags: true,
            border: {
                type: 'line',
            },
            style: {
                fg: 'white',
                bg: 'blue',
                border: {
                    fg: 'cyan',
                },
            },
        });
        if (title) {
            dialog.setLabel(title);
        }
        dialog.ask(message, (err, value) => {
            dialog.destroy();
            screen.render();
            resolve(Boolean(value));
        });
        screen.render();
    });
}
/**
 * Create input dialog
 */
export function createInputDialog(screen, message, title, defaultValue) {
    return new Promise((resolve) => {
        const dialog = blessed.prompt({
            parent: screen,
            top: 'center',
            left: 'center',
            width: '50%',
            height: 'shrink',
            content: message,
            tags: true,
            border: {
                type: 'line',
            },
            style: {
                fg: 'white',
                bg: 'blue',
                border: {
                    fg: 'cyan',
                },
            },
        });
        if (title) {
            dialog.setLabel(title);
        }
        dialog.input(message, defaultValue || '', (err, value) => {
            dialog.destroy();
            screen.render();
            resolve(value || null);
        });
        screen.render();
    });
}
export default confirmationManager;
//# sourceMappingURL=use-confirmation.js.map