/**
 * Input Processing Utilities
 *
 * Handles text and image input processing for multi-modal AI interactions,
 * including file tag expansion and image optimization.
 */
import { readFileSync, existsSync, statSync } from 'fs';
import { extname, resolve } from 'path';
import { lookup } from 'mime-types';
import sharp from 'sharp';
import { getErrorMessage } from './error-utils.js';
// Supported image formats
const SUPPORTED_IMAGE_FORMATS = ['.png', '.jpg', '.jpeg', '.webp', '.gif'];
// Maximum image size (in bytes) - 20MB
const MAX_IMAGE_SIZE = 20 * 1024 * 1024;
// Maximum image dimensions for optimization
const MAX_IMAGE_WIDTH = 2048;
const MAX_IMAGE_HEIGHT = 2048;
/**
 * Create input item from text and image paths
 */
export async function createInputItem(text, imagePaths = []) {
    const content = [];
    // Add text content if provided
    if (text.trim()) {
        // Expand file tags in text
        const expandedText = await expandFileTags(text);
        content.push({
            type: 'input_text',
            text: expandedText,
        });
    }
    // Process image files
    for (const imagePath of imagePaths) {
        try {
            const imageData = await processImage(imagePath);
            content.push({
                type: 'input_image',
                image: imageData,
            });
        }
        catch (error) {
            console.warn(`Failed to process image ${imagePath}:`, error);
            // Add error message to text content
            content.push({
                type: 'input_text',
                text: `[Error: Could not process image ${imagePath}: ${getErrorMessage(error)}]`,
            });
        }
    }
    return {
        role: 'user',
        content,
        type: 'message',
        timestamp: Date.now(),
    };
}
/**
 * Process image file for AI consumption
 */
export async function processImage(imagePath) {
    const resolvedPath = resolve(imagePath);
    if (!existsSync(resolvedPath)) {
        throw new Error(`Image file not found: ${imagePath}`);
    }
    const stats = statSync(resolvedPath);
    if (stats.size > MAX_IMAGE_SIZE) {
        throw new Error(`Image file too large: ${stats.size} bytes (max: ${MAX_IMAGE_SIZE})`);
    }
    const ext = extname(resolvedPath).toLowerCase();
    if (!SUPPORTED_IMAGE_FORMATS.includes(ext)) {
        throw new Error(`Unsupported image format: ${ext}`);
    }
    const mimeType = lookup(resolvedPath) || 'image/jpeg';
    try {
        // Read and potentially optimize the image
        let imageBuffer = readFileSync(resolvedPath);
        // Optimize image if it's too large
        const image = sharp(imageBuffer);
        const metadata = await image.metadata();
        if (metadata.width && metadata.height) {
            if (metadata.width > MAX_IMAGE_WIDTH || metadata.height > MAX_IMAGE_HEIGHT) {
                imageBuffer = await image
                    .resize(MAX_IMAGE_WIDTH, MAX_IMAGE_HEIGHT, {
                    fit: 'inside',
                    withoutEnlargement: true,
                })
                    .jpeg({ quality: 85 })
                    .toBuffer();
            }
        }
        // Convert to base64
        const base64Data = imageBuffer.toString('base64');
        const dataUrl = `data:${mimeType};base64,${base64Data}`;
        return {
            data: dataUrl,
            mimeType,
        };
    }
    catch (error) {
        throw new Error(`Failed to process image: ${getErrorMessage(error)}`);
    }
}
/**
 * Expand file tags (@file.txt) in text to XML blocks with file contents
 */
export async function expandFileTags(text) {
    // Pattern to match @file.txt or @path/to/file.ext
    const fileTagPattern = /@([^\s@]+)/g;
    let expandedText = text;
    const matches = [...text.matchAll(fileTagPattern)];
    for (const match of matches) {
        const filePath = match[1];
        const fullMatch = match[0];
        try {
            const resolvedPath = resolve(filePath);
            if (existsSync(resolvedPath)) {
                const stats = statSync(resolvedPath);
                if (stats.isFile()) {
                    // Check if it's a text file (not binary)
                    if (isTextFile(resolvedPath)) {
                        const content = readFileSync(resolvedPath, 'utf-8');
                        const xmlBlock = createFileXmlBlock(filePath, content);
                        expandedText = expandedText.replace(fullMatch, xmlBlock);
                    }
                    else {
                        // For binary files, just mention the file
                        expandedText = expandedText.replace(fullMatch, `[Binary file: ${filePath}]`);
                    }
                }
                else {
                    expandedText = expandedText.replace(fullMatch, `[Directory: ${filePath}]`);
                }
            }
            else {
                expandedText = expandedText.replace(fullMatch, `[File not found: ${filePath}]`);
            }
        }
        catch (error) {
            expandedText = expandedText.replace(fullMatch, `[Error reading ${filePath}: ${getErrorMessage(error)}]`);
        }
    }
    return expandedText;
}
/**
 * Create XML block for file content
 */
function createFileXmlBlock(filePath, content) {
    return `<file path="${filePath}">
${content}
</file>`;
}
/**
 * Check if file is a text file (not binary)
 */
function isTextFile(filePath) {
    const textExtensions = [
        '.txt', '.md', '.js', '.ts', '.tsx', '.jsx', '.py', '.java', '.c', '.cpp',
        '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
        '.html', '.htm', '.css', '.scss', '.sass', '.less', '.xml', '.json', '.yaml',
        '.yml', '.toml', '.ini', '.cfg', '.conf', '.sh', '.bash', '.zsh', '.fish',
        '.ps1', '.bat', '.cmd', '.sql', '.r', '.m', '.pl', '.lua', '.vim', '.emacs',
        '.gitignore', '.gitattributes', '.dockerignore', '.editorconfig',
    ];
    const ext = extname(filePath).toLowerCase();
    if (textExtensions.includes(ext)) {
        return true;
    }
    // Check for files without extensions that are typically text
    const basename = filePath.split('/').pop() || '';
    const textFilenames = [
        'README', 'LICENSE', 'CHANGELOG', 'CONTRIBUTING', 'AUTHORS', 'INSTALL',
        'Makefile', 'Dockerfile', 'Vagrantfile', 'Gemfile', 'Rakefile',
    ];
    return textFilenames.includes(basename);
}
/**
 * Collapse XML blocks back to file tags
 */
export function collapseXmlBlocks(text) {
    const xmlBlockPattern = /<file path="([^"]+)">\n([\s\S]*?)\n<\/file>/g;
    return text.replace(xmlBlockPattern, '@$1');
}
/**
 * Extract file paths from text (both @file.txt and XML blocks)
 */
export function extractFilePaths(text) {
    const paths = [];
    // Extract from @file.txt patterns
    const fileTagPattern = /@([^\s@]+)/g;
    const fileTagMatches = [...text.matchAll(fileTagPattern)];
    paths.push(...fileTagMatches.map(match => match[1]));
    // Extract from XML blocks
    const xmlBlockPattern = /<file path="([^"]+)">/g;
    const xmlMatches = [...text.matchAll(xmlBlockPattern)];
    paths.push(...xmlMatches.map(match => match[1]));
    // Remove duplicates and return
    return [...new Set(paths)];
}
/**
 * Validate input content
 */
export function validateInputContent(content) {
    const errors = [];
    if (content.length === 0) {
        errors.push('Input content cannot be empty');
    }
    for (const item of content) {
        if (item.type === 'input_text') {
            if (!item.text || item.text.trim().length === 0) {
                errors.push('Text content cannot be empty');
            }
        }
        else if (item.type === 'input_image') {
            if (!item.image || (!item.image.data && !item.image.url)) {
                errors.push('Image content must have data or URL');
            }
        }
    }
    return errors;
}
/**
 * Get content statistics
 */
export function getContentStats(content) {
    let textItems = 0;
    let imageItems = 0;
    let totalTextLength = 0;
    for (const item of content) {
        if (item.type === 'input_text' && item.text) {
            textItems++;
            totalTextLength += item.text.length;
        }
        else if (item.type === 'input_image') {
            imageItems++;
        }
    }
    // Rough token estimation (4 chars per token)
    const estimatedTokens = Math.ceil(totalTextLength / 4);
    return {
        textItems,
        imageItems,
        totalTextLength,
        estimatedTokens,
    };
}
//# sourceMappingURL=input-utils.js.map