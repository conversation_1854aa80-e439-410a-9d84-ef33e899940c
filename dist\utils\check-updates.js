/**
 * Update Checking System
 *
 * Automatically checks for newer versions of the CLI and displays
 * update notifications with appropriate package manager commands.
 */
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { execSync } from 'child_process';
import semver from 'semver';
import chalk from 'chalk';
import { CLI_VERSION } from '../version.js';
import { detectInstallerByPath } from './package-manager-detector.js';
import { logInfo, logWarn, logError } from './logger/log.js';
import { toError, getErrorMessage } from './error-utils.js';
// Update check configuration
const UPDATE_CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
const UPDATE_CHECK_FILE = join(homedir(), '.kritrima-ai', 'last-update-check.json');
const NPM_REGISTRY_URL = 'https://registry.npmjs.org/kritrima-ai-cli';
/**
 * Check for updates and display notification if available
 */
export async function checkForUpdates(force = false) {
    try {
        // Skip if not forced and recently checked
        if (!force && !shouldCheckForUpdates()) {
            return;
        }
        logInfo('Checking for updates...');
        // Fetch latest version from npm registry
        const latestVersion = await fetchLatestVersion();
        if (!latestVersion) {
            logWarn('Could not fetch latest version information');
            return;
        }
        // Update last check time
        updateLastCheckTime(latestVersion);
        // Compare versions
        if (semver.gt(latestVersion, CLI_VERSION)) {
            await displayUpdateNotification(latestVersion);
        }
        else {
            logInfo('CLI is up to date');
            if (force) {
                console.log(chalk.green('✓ Kritrima AI CLI is up to date'));
            }
        }
    }
    catch (error) {
        logError('Update check failed', toError(error));
        if (force) {
            console.error(chalk.red('Failed to check for updates:'), getErrorMessage(error));
        }
    }
}
/**
 * Check if we should perform an update check
 */
function shouldCheckForUpdates() {
    try {
        if (!existsSync(UPDATE_CHECK_FILE)) {
            return true;
        }
        const data = JSON.parse(readFileSync(UPDATE_CHECK_FILE, 'utf-8'));
        const timeSinceLastCheck = Date.now() - data.lastCheck;
        return timeSinceLastCheck > UPDATE_CHECK_INTERVAL;
    }
    catch (error) {
        return true; // Check on error
    }
}
/**
 * Fetch latest version from npm registry
 */
async function fetchLatestVersion() {
    try {
        // Use curl or fetch to get package info
        const response = await fetchWithTimeout(NPM_REGISTRY_URL, 10000);
        const packageInfo = JSON.parse(response);
        return packageInfo['dist-tags'].latest;
    }
    catch (error) {
        logError('Failed to fetch version from npm registry', toError(error));
        return null;
    }
}
/**
 * Fetch with timeout using curl (cross-platform)
 */
async function fetchWithTimeout(url, timeout) {
    try {
        const result = execSync(`curl -s --max-time ${Math.floor(timeout / 1000)} "${url}"`, {
            encoding: 'utf8',
            timeout,
        });
        return result;
    }
    catch (error) {
        throw new Error(`Failed to fetch ${url}: ${getErrorMessage(error)}`);
    }
}
/**
 * Update last check time
 */
function updateLastCheckTime(latestVersion) {
    try {
        const dir = dirname(UPDATE_CHECK_FILE);
        if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
        }
        const data = {
            lastCheck: Date.now(),
            lastVersion: latestVersion,
        };
        writeFileSync(UPDATE_CHECK_FILE, JSON.stringify(data, null, 2));
    }
    catch (error) {
        logWarn('Failed to update last check time', error);
    }
}
/**
 * Display update notification
 */
async function displayUpdateNotification(latestVersion) {
    const packageManager = await detectInstallerByPath();
    const updateCommand = getUpdateCommand(packageManager);
    console.log('');
    console.log(chalk.cyan('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.cyan('║                    Update Available                         ║'));
    console.log(chalk.cyan('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
    console.log(chalk.yellow(`  Current version: ${CLI_VERSION}`));
    console.log(chalk.green(`  Latest version:  ${latestVersion}`));
    console.log('');
    console.log(chalk.blue('  To update, run:'));
    console.log(chalk.white(`    ${updateCommand}`));
    console.log('');
    console.log(chalk.gray('  Release notes: https://github.com/kritrima/kritrima-ai-cli/releases'));
    console.log('');
    logInfo(`Update available: ${CLI_VERSION} -> ${latestVersion}`);
}
/**
 * Get appropriate update command for package manager
 */
function getUpdateCommand(packageManager) {
    switch (packageManager) {
        case 'npm':
            return 'npm update -g kritrima-ai-cli';
        case 'pnpm':
            return 'pnpm update -g kritrima-ai-cli';
        case 'yarn':
            return 'yarn global upgrade kritrima-ai-cli';
        case 'bun':
            return 'bun update -g kritrima-ai-cli';
        case 'deno':
            return 'deno install -A -f -n kritrima-ai https://deno.land/x/kritrima_ai_cli/mod.ts';
        default:
            return 'npm update -g kritrima-ai-cli';
    }
}
/**
 * Check if version is dismissed
 */
function isVersionDismissed(version) {
    try {
        if (!existsSync(UPDATE_CHECK_FILE)) {
            return false;
        }
        const data = JSON.parse(readFileSync(UPDATE_CHECK_FILE, 'utf-8'));
        return data.dismissed?.includes(version) || false;
    }
    catch (error) {
        return false;
    }
}
/**
 * Dismiss version update
 */
export function dismissVersion(version) {
    try {
        const dir = dirname(UPDATE_CHECK_FILE);
        if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
        }
        let data = { lastCheck: Date.now() };
        if (existsSync(UPDATE_CHECK_FILE)) {
            data = JSON.parse(readFileSync(UPDATE_CHECK_FILE, 'utf-8'));
        }
        if (!data.dismissed) {
            data.dismissed = [];
        }
        if (!data.dismissed.includes(version)) {
            data.dismissed.push(version);
        }
        writeFileSync(UPDATE_CHECK_FILE, JSON.stringify(data, null, 2));
        console.log(chalk.yellow(`Dismissed update notification for version ${version}`));
    }
    catch (error) {
        logError('Failed to dismiss version', toError(error));
    }
}
/**
 * Clear update check cache
 */
export function clearUpdateCache() {
    try {
        if (existsSync(UPDATE_CHECK_FILE)) {
            writeFileSync(UPDATE_CHECK_FILE, JSON.stringify({ lastCheck: 0 }, null, 2));
            console.log(chalk.green('Update check cache cleared'));
        }
    }
    catch (error) {
        logError('Failed to clear update cache', toError(error));
    }
}
/**
 * Get update status
 */
export async function getUpdateStatus() {
    const latestVersion = await fetchLatestVersion();
    const updateAvailable = latestVersion ? semver.gt(latestVersion, CLI_VERSION) : false;
    let lastCheck;
    try {
        if (existsSync(UPDATE_CHECK_FILE)) {
            const data = JSON.parse(readFileSync(UPDATE_CHECK_FILE, 'utf-8'));
            lastCheck = data.lastCheck;
        }
    }
    catch (error) {
        // Ignore error
    }
    return {
        currentVersion: CLI_VERSION,
        latestVersion: latestVersion || undefined,
        updateAvailable,
        lastCheck,
    };
}
/**
 * Force update check
 */
export async function forceUpdateCheck() {
    await checkForUpdates(true);
}
/**
 * Schedule automatic update checks
 */
export function scheduleUpdateChecks() {
    // Check for updates on startup (non-blocking)
    setTimeout(() => {
        checkForUpdates().catch(() => {
            // Ignore errors in background check
        });
    }, 1000);
    // Schedule periodic checks
    setInterval(() => {
        checkForUpdates().catch(() => {
            // Ignore errors in background check
        });
    }, UPDATE_CHECK_INTERVAL);
}
//# sourceMappingURL=check-updates.js.map