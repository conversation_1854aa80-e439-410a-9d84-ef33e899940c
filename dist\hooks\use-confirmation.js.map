{"version": 3, "file": "use-confirmation.js", "sourceRoot": "", "sources": ["../../src/hooks/use-confirmation.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,OAAO,MAAM,SAAS,CAAC;AAiB9B,MAAM,mBAAoB,SAAQ,YAAY;IACpC,KAAK,GAA0B,EAAE,CAAC;IAClC,cAAc,GAA+B,IAAI,CAAC;IAClD,YAAY,GAAG,KAAK,CAAC;IAE7B;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,MAAsB,EACtB,WAAoB;QAEpB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAwB;gBACnC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC3C,MAAM;gBACN,WAAW;gBACX,OAAO;gBACP,MAAM;aACP,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,MAA0B;QAC3C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,MAAe;QAChC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,wBAAwB,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,8BAA8B;QAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACjC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;YAChE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAG,CAAC;QAE1C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3D,CAAC;CACF;AAED,uCAAuC;AACvC,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAEtD;;GAEG;AACH,MAAM,UAAU,eAAe;IAU7B,MAAM,cAAc,GAAG,mBAAmB,CAAC,iBAAiB,EAAE,CAAC;IAE/D,OAAO;QACL,kBAAkB,EAAE,CAAC,MAA0B,EAAE,EAAE;YACjD,mBAAmB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;QACD,mBAAmB,EAAE,CAAC,MAAsB,EAAE,WAAoB,EAAE,EAAE;YACpE,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACtE,CAAC;QACD,kBAAkB,EAAE,cAAc,EAAE,MAAM,IAAI,IAAI;QAClD,WAAW,EAAE,cAAc,EAAE,WAAW;QACxC,SAAS,EAAE,mBAAmB,CAAC,SAAS,EAAE;KAC3C,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,wBAAwB,CACtC,MAA8B,EAC9B,OAAe,EACf,UAKI,EAAE;IAEN,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;QACzB,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,QAAQ;QACb,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,KAAK;QAC7B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,QAAQ;QAClC,OAAO,EAAE,OAAO;QAChB,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC;YACjC,IAAI,EAAE,MAAM;SACb,CAAC,CAAC,CAAC,SAAS;QACb,KAAK,EAAE;YACL,EAAE,EAAE,OAAO;YACX,EAAE,EAAE,MAAM;YACV,MAAM,EAAE;gBACN,EAAE,EAAE,MAAM;aACX;SACF;QACD,OAAO,EAAE;YACP,GAAG,EAAE,CAAC;YACN,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;SACT;KACF,CAAC,CAAC;IAEH,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAC/B,MAA8B,EAC9B,OAAe,EACf,KAAc;IAEd,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;YAC9B,MAAM,EAAE,MAAM;YACd,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,IAAI;YACV,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO;gBACX,EAAE,EAAE,MAAM;gBACV,MAAM,EAAE;oBACN,EAAE,EAAE,MAAM;iBACX;aACF;SACF,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QAED,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACjC,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAC/B,MAA8B,EAC9B,OAAe,EACf,KAAc,EACd,YAAqB;IAErB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAC5B,MAAM,EAAE,MAAM;YACd,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,IAAI;YACV,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO;gBACX,EAAE,EAAE,MAAM;gBACV,MAAM,EAAE;oBACN,EAAE,EAAE,MAAM;iBACX;aACF;SACF,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACvD,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,mBAAmB,CAAC"}