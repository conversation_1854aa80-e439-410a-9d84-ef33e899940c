/**
 * OpenAI Client Factory
 *
 * Creates provider-specific OpenAI client instances with proper configuration
 * for different AI providers using the OpenAI-compatible API format.
 */
import { OpenAI } from 'openai';
import type { AppConfig } from '../types/index.js';
export interface ClientConfig {
    provider?: string;
    model?: string;
    apiKey?: string;
    baseURL?: string;
    timeout?: number;
    organization?: string;
    project?: string;
    defaultHeaders?: Record<string, string>;
}
/**
 * Create an OpenAI client instance for the specified provider
 */
export declare function createOpenAIClient(config?: ClientConfig): OpenAI;
/**
 * Create client from app configuration
 */
export declare function createClientFromConfig(appConfig: AppConfig): OpenAI;
/**
 * Test client connection and authentication
 */
export declare function testClientConnection(client: OpenAI, model?: string): Promise<boolean>;
/**
 * Get client capabilities based on provider
 */
export declare function getClientCapabilities(provider: string): {
    supportsImages: boolean;
    supportsTools: boolean;
    supportsStreaming: boolean;
    maxContextLength: number;
};
/**
 * Validate client configuration
 */
export declare function validateClientConfig(config: ClientConfig): string[];
/**
 * Create multiple clients for different providers
 */
export declare function createMultipleClients(providers: string[]): Record<string, OpenAI>;
/**
 * Get recommended timeout for provider
 */
export declare function getRecommendedTimeout(provider: string): number;
//# sourceMappingURL=openai-client.d.ts.map