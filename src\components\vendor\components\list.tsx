/**
 * Enhanced List Component
 * 
 * Wrapper around blessed.list with additional functionality
 * for better keyboard navigation and item management.
 */

import * as blessed from 'blessed';
import { EventEmitter } from 'events';
import { logDebug } from '../../../utils/logger/log.js';

export interface ListItem {
  text: string;
  value?: any;
  data?: any;
  disabled?: boolean;
  separator?: boolean;
}

export interface EnhancedListOptions extends Omit<blessed.Widgets.ListOptions<blessed.Widgets.ListElementStyle>, 'items'> {
  items?: ListItem[] | string[];
  onSelect?: (item: ListItem | string, index: number) => void;
  onCancel?: () => void;
  onHighlight?: (item: ListItem | string, index: number) => void;
  searchable?: boolean;
  multiSelect?: boolean;
  emptyMessage?: string;
  loadingMessage?: string;
}

export class EnhancedList extends EventEmitter {
  private list: blessed.Widgets.ListElement;
  private options: EnhancedListOptions;
  private items: (ListItem | string)[] = [];
  private filteredItems: (ListItem | string)[] = [];
  private selectedIndices = new Set<number>();
  private searchQuery = '';
  private isLoading = false;

  constructor(options: EnhancedListOptions) {
    super();
    
    this.options = options;
    this.items = options.items || [];
    this.filteredItems = [...this.items];

    // Create blessed list with enhanced defaults
    const { items, ...listOptions } = options;
    this.list = blessed.list({
      keys: true,
      vi: true,
      mouse: true,
      scrollable: true,
      alwaysScroll: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'cyan',
        },
        style: {
          inverse: true,
        },
      },
      style: {
        selected: {
          bg: 'blue',
          fg: 'white',
        },
        item: {
          fg: 'white',
        },
        focus: {
          border: {
            fg: 'cyan',
          },
        },
      },
      border: {
        type: 'line',
      },
      items: [], // Initialize with empty array, will be set in updateDisplay
      ...listOptions,
    });

    this.setupEventHandlers();
    this.updateDisplay();
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Handle item selection
    this.list.on('select', (item, index) => {
      const actualIndex = this.getActualIndex(index);
      const actualItem = this.items[actualIndex];
      
      if (this.isItemDisabled(actualItem)) {
        return;
      }

      if (this.options.multiSelect) {
        this.toggleSelection(actualIndex);
      }

      if (this.options.onSelect) {
        this.options.onSelect(actualItem, actualIndex);
      }
      
      this.emit('select', actualItem, actualIndex);
    });

    // Handle item highlighting
    this.list.on('select item', (item, index) => {
      const actualIndex = this.getActualIndex(index);
      const actualItem = this.items[actualIndex];
      
      if (this.options.onHighlight) {
        this.options.onHighlight(actualItem, actualIndex);
      }
      
      this.emit('highlight', actualItem, actualIndex);
    });

    // Handle cancellation
    this.list.key(['escape', 'C-c'], () => {
      if (this.options.onCancel) {
        this.options.onCancel();
      }
      
      this.emit('cancel');
    });

    // Handle search if enabled
    if (this.options.searchable) {
      this.setupSearchHandlers();
    }

    // Handle multi-select if enabled
    if (this.options.multiSelect) {
      this.setupMultiSelectHandlers();
    }

    // Handle page navigation
    this.list.key(['pageup', 'C-b'], () => {
      this.pageUp();
    });

    this.list.key(['pagedown', 'C-f'], () => {
      this.pageDown();
    });

    // Handle home/end
    this.list.key(['home', 'C-a'], () => {
      this.selectFirst();
    });

    this.list.key(['end', 'C-e'], () => {
      this.selectLast();
    });
  }

  /**
   * Setup search handlers
   */
  private setupSearchHandlers(): void {
    this.list.key(['/', 'C-s'], () => {
      this.startSearch();
    });

    this.list.key(['n'], () => {
      this.searchNext();
    });

    this.list.key(['N'], () => {
      this.searchPrevious();
    });
  }

  /**
   * Setup multi-select handlers
   */
  private setupMultiSelectHandlers(): void {
    this.list.key(['space'], () => {
      const index = (this.list as any).selected || 0;
      const actualIndex = this.getActualIndex(index);
      this.toggleSelection(actualIndex);
    });

    this.list.key(['a'], () => {
      this.selectAll();
    });

    this.list.key(['A'], () => {
      this.deselectAll();
    });
  }

  /**
   * Get the underlying blessed list
   */
  getList(): blessed.Widgets.ListElement {
    return this.list;
  }

  /**
   * Set items
   */
  setItems(items: (ListItem | string)[]): void {
    this.items = items;
    this.filteredItems = [...items];
    this.selectedIndices.clear();
    this.updateDisplay();
  }

  /**
   * Add item
   */
  addItem(item: ListItem | string): void {
    this.items.push(item);
    this.filteredItems = [...this.items];
    this.updateDisplay();
  }

  /**
   * Remove item
   */
  removeItem(index: number): void {
    if (index >= 0 && index < this.items.length) {
      this.items.splice(index, 1);
      this.filteredItems = [...this.items];
      this.selectedIndices.delete(index);
      this.updateDisplay();
    }
  }

  /**
   * Clear all items
   */
  clear(): void {
    this.items = [];
    this.filteredItems = [];
    this.selectedIndices.clear();
    this.updateDisplay();
  }

  /**
   * Get selected item
   */
  getSelected(): { item: ListItem | string; index: number } | null {
    const index = (this.list as any).selected || 0;
    const actualIndex = this.getActualIndex(index);
    const item = this.items[actualIndex];

    return item ? { item, index: actualIndex } : null;
  }

  /**
   * Get all selected items (for multi-select)
   */
  getSelectedItems(): Array<{ item: ListItem | string; index: number }> {
    return Array.from(this.selectedIndices).map(index => ({
      item: this.items[index],
      index,
    }));
  }

  /**
   * Select item by index
   */
  select(index: number): void {
    if (index >= 0 && index < this.filteredItems.length) {
      this.list.select(index);
    }
  }

  /**
   * Focus the list
   */
  focus(): void {
    this.list.focus();
  }

  /**
   * Show loading state
   */
  showLoading(): void {
    this.isLoading = true;
    this.updateDisplay();
  }

  /**
   * Hide loading state
   */
  hideLoading(): void {
    this.isLoading = false;
    this.updateDisplay();
  }

  /**
   * Update display
   */
  private updateDisplay(): void {
    let displayItems: string[];

    if (this.isLoading) {
      displayItems = [this.options.loadingMessage || 'Loading...'];
    } else if (this.filteredItems.length === 0) {
      displayItems = [this.options.emptyMessage || 'No items'];
    } else {
      displayItems = this.filteredItems.map((item, index) => {
        const actualIndex = this.getActualIndex(index);
        const isSelected = this.selectedIndices.has(actualIndex);
        const prefix = this.options.multiSelect && isSelected ? '✓ ' : '';
        
        if (typeof item === 'string') {
          return prefix + item;
        } else {
          const disabled = item.disabled ? ' (disabled)' : '';
          return prefix + item.text + disabled;
        }
      });
    }

    this.list.setItems(displayItems);
    
    if (this.list.screen) {
      this.list.screen.render();
    }
  }

  /**
   * Get actual item index from filtered index
   */
  private getActualIndex(filteredIndex: number): number {
    if (this.searchQuery) {
      // Find the actual index in the original items array
      const filteredItem = this.filteredItems[filteredIndex];
      return this.items.indexOf(filteredItem);
    }
    return filteredIndex;
  }

  /**
   * Check if item is disabled
   */
  private isItemDisabled(item: ListItem | string): boolean {
    return typeof item === 'object' && item.disabled === true;
  }

  /**
   * Toggle selection for multi-select
   */
  private toggleSelection(index: number): void {
    if (this.selectedIndices.has(index)) {
      this.selectedIndices.delete(index);
    } else {
      this.selectedIndices.add(index);
    }
    this.updateDisplay();
  }

  /**
   * Select all items
   */
  private selectAll(): void {
    for (let i = 0; i < this.items.length; i++) {
      if (!this.isItemDisabled(this.items[i])) {
        this.selectedIndices.add(i);
      }
    }
    this.updateDisplay();
  }

  /**
   * Deselect all items
   */
  private deselectAll(): void {
    this.selectedIndices.clear();
    this.updateDisplay();
  }

  /**
   * Page up
   */
  private pageUp(): void {
    const pageSize = Math.floor((this.list.height as number || 10) * 0.8);
    const newIndex = Math.max(0, ((this.list as any).selected || 0) - pageSize);
    this.list.select(newIndex);
  }

  /**
   * Page down
   */
  private pageDown(): void {
    const pageSize = Math.floor((this.list.height as number || 10) * 0.8);
    const newIndex = Math.min(this.filteredItems.length - 1, ((this.list as any).selected || 0) + pageSize);
    this.list.select(newIndex);
  }

  /**
   * Select first item
   */
  private selectFirst(): void {
    this.list.select(0);
  }

  /**
   * Select last item
   */
  private selectLast(): void {
    this.list.select(this.filteredItems.length - 1);
  }

  /**
   * Start search
   */
  private startSearch(): void {
    // TODO: Implement search functionality
    logDebug('Search functionality not yet implemented');
  }

  /**
   * Search next
   */
  private searchNext(): void {
    // TODO: Implement search next
    logDebug('Search next functionality not yet implemented');
  }

  /**
   * Search previous
   */
  private searchPrevious(): void {
    // TODO: Implement search previous
    logDebug('Search previous functionality not yet implemented');
  }

  /**
   * Destroy the list
   */
  destroy(): void {
    this.list.destroy();
    this.removeAllListeners();
  }
}

export default EnhancedList;
