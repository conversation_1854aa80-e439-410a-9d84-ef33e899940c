/**
 * Update Checking System
 *
 * Automatically checks for newer versions of the CLI and displays
 * update notifications with appropriate package manager commands.
 */
/**
 * Check for updates and display notification if available
 */
export declare function checkForUpdates(force?: boolean): Promise<void>;
/**
 * Dismiss version update
 */
export declare function dismissVersion(version: string): void;
/**
 * Clear update check cache
 */
export declare function clearUpdateCache(): void;
/**
 * Get update status
 */
export declare function getUpdateStatus(): Promise<{
    currentVersion: string;
    latestVersion?: string;
    updateAvailable: boolean;
    lastCheck?: number;
}>;
/**
 * Force update check
 */
export declare function forceUpdateCheck(): Promise<void>;
/**
 * Schedule automatic update checks
 */
export declare function scheduleUpdateChecks(): void;
//# sourceMappingURL=check-updates.d.ts.map