{"version": 3, "file": "context-limit.js", "sourceRoot": "", "sources": ["../../../src/utils/singlepass/context-limit.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAmBrD;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,KAAoB;IACjD,MAAM,OAAO,GAAY;QACvB,UAAU,EAAE,KAAK,CAAC,MAAM;QACxB,SAAS,EAAE,CAAC;QACZ,WAAW,EAAE,EAAE;QACf,UAAU,EAAE,EAAE;QACd,YAAY,EAAE,EAAE;KACjB,CAAC;IAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,IAAI,cAAc,CAAC;QAE/D,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC;QAC/B,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/D,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QAErE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAClE,CAAC;IAED,qBAAqB;IACrB,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACrD,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS;IAEnE,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,wBAAwB,CACtC,KAAoB,EACpB,MAAqB;IAErB,OAAO,CAAC,cAAc,KAAK,CAAC,MAAM,2BAA2B,CAAC,CAAC;IAE/D,8BAA8B;IAC9B,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC;IAC3E,QAAQ,CAAC,yBAAyB,YAAY,CAAC,MAAM,QAAQ,CAAC,CAAC;IAE/D,8BAA8B;IAC9B,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,8BAA8B,iBAAiB,CAAC,MAAM,QAAQ,CAAC,CAAC;IAEzE,2BAA2B;IAC3B,MAAM,WAAW,GAAG,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,kBAAkB,CAAC,CAAC;IAElF,0CAA0C;IAC1C,MAAM,QAAQ,GAAG,2BAA2B,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;IAE5E,2CAA2C;IAC3C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;IAEjD,OAAO,CAAC,oBAAoB,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;IAClD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,KAAoB,EAAE,kBAA4B;IACzE,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACzB,MAAM,MAAM,GAAG,uBAAuB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAC9D,MAAM,MAAM,GAAG,uBAAuB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAE9D,OAAO,MAAM,GAAG,MAAM,CAAC,CAAC,qBAAqB;IAC/C,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAAC,IAAiB,EAAE,kBAA4B;IAC9E,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IAE/C,wBAAwB;IACxB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACjD,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC;QACpB,KAAK,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;IACvD,CAAC;IAED,uBAAuB;IACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IAC1C,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC,CAAC,2BAA2B;IAE9F,6CAA6C;IAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IAC1C,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC;IAEjC,+BAA+B;IAC/B,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IACzC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAAA,KAAK,IAAI,EAAE,CAAC;IAAA,CAAC;IAE1C,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,2BAA2B,CAAC,KAAoB,EAAE,SAAiB;IAC1E,MAAM,QAAQ,GAAkB,EAAE,CAAC;IACnC,IAAI,WAAW,GAAG,CAAC,CAAC;IAEpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAI,WAAW,GAAG,UAAU,IAAI,SAAS,EAAE,CAAC;YAC1C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpB,WAAW,IAAI,UAAU,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,qCAAqC;YACrC,MAAM,eAAe,GAAG,SAAS,GAAG,WAAW,CAAC;YAChD,IAAI,eAAe,GAAG,GAAG,EAAE,CAAC,CAAC,wCAAwC;gBACnE,MAAM,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;gBACxE,QAAQ,CAAC,IAAI,CAAC;oBACZ,GAAG,IAAI;oBACP,OAAO,EAAE,gBAAgB;oBACzB,IAAI,EAAE,gBAAgB,CAAC,MAAM;iBAC9B,CAAC,CAAC;gBACH,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,oCAAoC,WAAW,SAAS,CAAC,CAAC;IACnE,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,IAAY;IAClC,qDAAqD;IACrD,4CAA4C;IAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,eAAe;IAE7D,OAAO,UAAU,GAAG,QAAQ,CAAC;AAC/B,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,OAAe,EAAE,SAAiB;IACzD,MAAM,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,mBAAmB;IAEnD,IAAI,OAAO,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;QAC/B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,2CAA2C;IAC3C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,SAAS,GAAG,EAAE,CAAC;IAEnB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC,CAAC,eAAe;YACzE,SAAS,IAAI,IAAI,GAAG,IAAI,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,MAAM;QACR,CAAC;IACH,CAAC;IAED,SAAS,IAAI,oDAAoD,CAAC;IAElE,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAAC,KAAa;IACnD,MAAM,MAAM,GAAkB;QAC5B,SAAS,EAAE,KAAK,EAAE,uBAAuB;QACzC,QAAQ,EAAE,EAAE;QACZ,WAAW,EAAE,GAAG,GAAG,IAAI,EAAE,QAAQ;QACjC,kBAAkB,EAAE;YAClB,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;YAC5B,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK;YAC5B,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM;YAC/B,OAAO,EAAE,MAAM,EAAE,OAAO;SACzB;QACD,iBAAiB,EAAE;YACjB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO;YACjC,SAAS,EAAE,UAAU;YACrB,MAAM,EAAE,OAAO;SAChB;KACF,CAAC;IAEF,wBAAwB;IACxB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7D,CAAC;SAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QACrC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;SAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;QACxC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,qBAAqB;QAChD,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC;IACxB,CAAC;SAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACtC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC;QAC1B,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC;IACxB,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,KAAoB;IAMtD,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,MAAM,YAAY,GAA2B,EAAE,CAAC;IAChD,MAAM,eAAe,GAAa,EAAE,CAAC;IAErC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,IAAI,cAAc,CAAC;QAC/D,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE5C,WAAW,IAAI,MAAM,CAAC;QACtB,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;IACxD,CAAC;IAED,oDAAoD;IACpD,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACpF,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1F,MAAM,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1E,2BAA2B;IAC3B,IAAI,UAAU,GAAG,EAAE,EAAE,CAAC;QACpB,eAAe,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;IAC7F,CAAC;IAED,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,GAAG,EAAE,CAAC;QACnE,eAAe,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;IAC9F,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QACvB,eAAe,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;IACxF,CAAC;IAED,OAAO;QACL,WAAW;QACX,YAAY;QACZ,UAAU;QACV,eAAe;KAChB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,IAAiB,EAAE,cAAsB;IACtE,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE5C,IAAI,MAAM,IAAI,cAAc,EAAE,CAAC;QAC7B,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,MAAM,MAAM,GAAkB,EAAE,CAAC;IACjC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QACxC,MAAM,WAAW,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC;QAEjD,IAAI,WAAW,GAAG,UAAU,GAAG,cAAc,IAAI,YAAY,EAAE,CAAC;YAC9D,qBAAqB;YACrB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,SAAS,UAAU,EAAE;gBACvC,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE,YAAY,CAAC,MAAM;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAC;YAEH,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC;YAC3B,UAAU,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,kBAAkB;IAClB,IAAI,YAAY,EAAE,CAAC;QACjB,MAAM,CAAC,IAAI,CAAC;YACV,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,SAAS,UAAU,EAAE;YACvC,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE,YAAY,CAAC,MAAM;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}