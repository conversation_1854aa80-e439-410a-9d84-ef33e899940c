/**
 * Main Application Layer
 *
 * Handles application initialization, Git validation, safety checks,
 * and UI orchestration for the Kritrima AI CLI.
 */
import type { AppConfig } from './types/index';
/**
 * Run the main application
 */
export declare function runApp(config: AppConfig, initialPrompt?: string): Promise<void>;
/**
 * Handle graceful shutdown
 */
export declare function setupGracefulShutdown(): void;
/**
 * Display startup banner
 */
export declare function displayStartupBanner(config: AppConfig): void;
/**
 * Handle application errors
 */
export declare function handleApplicationError(error: Error): void;
/**
 * Initialize application with error handling
 */
export declare function initializeApp(config: AppConfig, initialPrompt?: string): Promise<void>;
//# sourceMappingURL=app.d.ts.map