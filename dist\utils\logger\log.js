/**
 * Comprehensive Logging System
 *
 * Multi-platform logging with async queue processing,
 * debug modes, and performance monitoring.
 */
import { writeFileSync, appendFileSync, existsSync, mkdirSync, symlinkSync, unlinkSync } from 'fs';
import { join, dirname } from 'path';
import { homedir, tmpdir } from 'os';
import { format } from 'date-fns';
/**
 * Utility function to safely cast unknown errors to Error objects
 */
export function toError(error) {
    if (error instanceof Error) {
        return error;
    }
    if (typeof error === 'string') {
        return new Error(error);
    }
    if (error && typeof error === 'object' && 'message' in error) {
        return new Error(String(error.message));
    }
    return new Error('Unknown error occurred');
}
/**
 * Async logger with queue processing
 */
class AsyncLogger {
    queue = [];
    isWriting = false;
    logPath;
    symlinkPath;
    constructor() {
        this.logPath = this.getLogPath();
        this.symlinkPath = this.getSymlinkPath();
        this.ensureLogDirectory();
        this.createSymlink();
    }
    /**
     * Get platform-specific log path
     */
    getLogPath() {
        const timestamp = format(new Date(), 'yyyy-MM-dd-HH-mm-ss');
        const filename = `kritrima-ai-cli-${timestamp}.log`;
        switch (process.platform) {
            case 'darwin':
            case 'win32':
                return join(tmpdir(), 'kritrima-ai', filename);
            case 'linux':
            default:
                return join(homedir(), '.local', 'kritrima-ai', filename);
        }
    }
    /**
     * Get symlink path for latest log
     */
    getSymlinkPath() {
        const logDir = dirname(this.logPath);
        return join(logDir, 'kritrima-ai-cli-latest.log');
    }
    /**
     * Ensure log directory exists
     */
    ensureLogDirectory() {
        const logDir = dirname(this.logPath);
        if (!existsSync(logDir)) {
            mkdirSync(logDir, { recursive: true });
        }
    }
    /**
     * Create symlink to latest log
     */
    createSymlink() {
        try {
            if (existsSync(this.symlinkPath)) {
                unlinkSync(this.symlinkPath);
            }
            symlinkSync(this.logPath, this.symlinkPath);
        }
        catch (error) {
            // Symlink creation may fail on some systems, ignore
        }
    }
    /**
     * Add message to queue
     */
    enqueue(level, message, error) {
        const timestamp = new Date().toISOString();
        let logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        if (error) {
            logEntry += `\n  Error: ${error.message}`;
            if (error.stack) {
                logEntry += `\n  Stack: ${error.stack}`;
            }
        }
        logEntry += '\n';
        this.queue.push(logEntry);
        this.maybeWrite();
    }
    /**
     * Process write queue
     */
    maybeWrite() {
        if (this.isWriting || this.queue.length === 0) {
            return;
        }
        this.isWriting = true;
        // Process queue in next tick to avoid blocking
        process.nextTick(() => {
            try {
                const entries = this.queue.splice(0);
                const content = entries.join('');
                if (existsSync(this.logPath)) {
                    appendFileSync(this.logPath, content);
                }
                else {
                    writeFileSync(this.logPath, content);
                }
            }
            catch (error) {
                // If logging fails, write to stderr as fallback
                console.error('Logging failed:', error);
            }
            finally {
                this.isWriting = false;
                // Process any new entries that arrived
                if (this.queue.length > 0) {
                    this.maybeWrite();
                }
            }
        });
    }
    log(message) {
        this.enqueue('info', message);
    }
    error(message, error) {
        this.enqueue('error', message, error);
    }
    warn(message) {
        this.enqueue('warn', message);
    }
    info(message) {
        this.enqueue('info', message);
    }
    debug(message) {
        if (isLoggingEnabled()) {
            this.enqueue('debug', message);
        }
    }
    performance(label, duration) {
        if (isLoggingEnabled()) {
            this.enqueue('perf', `${label}: ${duration}ms`);
        }
    }
}
/**
 * Console logger for development
 */
class ConsoleLogger {
    log(message) {
        console.log(`[LOG] ${message}`);
    }
    error(message, error) {
        console.error(`[ERROR] ${message}`, error);
    }
    warn(message) {
        console.warn(`[WARN] ${message}`);
    }
    info(message) {
        console.info(`[INFO] ${message}`);
    }
    debug(message) {
        if (isLoggingEnabled()) {
            console.debug(`[DEBUG] ${message}`);
        }
    }
    performance(label, duration) {
        if (isLoggingEnabled()) {
            console.log(`[PERF] ${label}: ${duration}ms`);
        }
    }
}
/**
 * No-op logger for tests
 */
class NoOpLogger {
    log() { }
    error() { }
    warn() { }
    info() { }
    debug() { }
    performance() { }
}
// Global logger instance
let loggerInstance = null;
/**
 * Check if logging is enabled
 */
export function isLoggingEnabled() {
    return process.env.DEBUG === '1' || process.env.KRITRIMA_AI_DEBUG === 'true';
}
/**
 * Get logger instance
 */
function getLogger() {
    if (loggerInstance) {
        return loggerInstance;
    }
    // Determine logger type based on environment
    if (process.env.NODE_ENV === 'test') {
        loggerInstance = new NoOpLogger();
    }
    else if (process.env.NODE_ENV === 'development' || isLoggingEnabled()) {
        loggerInstance = new ConsoleLogger();
    }
    else {
        loggerInstance = new AsyncLogger();
    }
    return loggerInstance;
}
/**
 * Set custom logger (for testing)
 */
export function setLogger(logger) {
    loggerInstance = logger;
}
/**
 * Log a message
 */
export function log(message) {
    getLogger().log(message);
}
/**
 * Log an error
 */
export function logError(message, error) {
    getLogger().error(message, error);
}
/**
 * Log a warning
 */
export function logWarn(message, details) {
    if (details) {
        getLogger().warn(`${message} ${JSON.stringify(details)}`);
    }
    else {
        getLogger().warn(message);
    }
}
/**
 * Log an info message
 */
export function logInfo(message) {
    getLogger().info(message);
}
/**
 * Log a debug message
 */
export function logDebug(message, details) {
    if (details) {
        getLogger().debug(`${message} ${JSON.stringify(details)}`);
    }
    else {
        getLogger().debug(message);
    }
}
/**
 * Log performance metrics
 */
export function logPerformance(label, duration) {
    getLogger().performance(label, duration);
}
/**
 * Performance timer utility
 */
export class PerformanceTimer {
    startTime;
    label;
    constructor(label) {
        this.label = label;
        this.startTime = Date.now();
    }
    end() {
        const duration = Date.now() - this.startTime;
        logPerformance(this.label, duration);
        return duration;
    }
}
/**
 * Create a performance timer
 */
export function createTimer(label) {
    return new PerformanceTimer(label);
}
/**
 * Log function execution time
 */
export function logExecutionTime(label, fn) {
    const timer = createTimer(label);
    try {
        const result = fn();
        if (result instanceof Promise) {
            return result.finally(() => timer.end());
        }
        else {
            timer.end();
            return result;
        }
    }
    catch (error) {
        timer.end();
        throw error;
    }
}
/**
 * FPS debugging for UI rendering
 */
export class FPSDebugger {
    frameCount = 0;
    lastTime = Date.now();
    fpsHistory = [];
    frame() {
        if (!isLoggingEnabled()) {
            return;
        }
        this.frameCount++;
        const now = Date.now();
        const elapsed = now - this.lastTime;
        if (elapsed >= 1000) {
            const fps = Math.round((this.frameCount * 1000) / elapsed);
            this.fpsHistory.push(fps);
            if (this.fpsHistory.length > 10) {
                this.fpsHistory.shift();
            }
            const avgFps = Math.round(this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length);
            logDebug(`FPS: ${fps} (avg: ${avgFps})`);
            this.frameCount = 0;
            this.lastTime = now;
        }
    }
}
/**
 * Global FPS debugger instance
 */
export const fpsDebugger = new FPSDebugger();
//# sourceMappingURL=log.js.map